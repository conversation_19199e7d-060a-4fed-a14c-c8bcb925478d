using System;

using System.Collections.Generic;
using System.Collections.Specialized;
using System.Configuration;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;
using AdminApp.Mapper;
using AdminApp.Services;
using Common.Lib;
using DBLinker.Lib;
using DBLinker.Lib.Model;
using DBLinker.Lib.Repository;
using Razorpay.Api;
using System.Text;
using System.Security.Cryptography;
using Newtonsoft.Json;

namespace AdminApp.Controllers
{

    public class BookingManagementController : Controller
    {
        DBLinker.Lib.RLTDBContext entity;
        CultureInfo provider = CultureInfo.InvariantCulture;
        IGenericRepository<RLT_LOG> logsRepo;
        IGenericRepository<RLT_BOOKING> BookingRepo;
        private readonly ISMSService smsService;
        ITwoFactorAuth auth;
        IResizeImage imageResize;
        ObjectMapper map;
        string isSMSEnabled = string.Empty;
        public BookingManagementController(ITwoFactorAuth _auth, IResizeImage _imageResize,
        IGenericRepository<RLT_BOOKING> _BookingRepo, ISMSService smsService)
        {

            imageResize = _imageResize;
            auth = _auth;
            map = new ObjectMapper();
            BookingRepo = _BookingRepo;
            this.smsService = smsService;
            entity = new DBLinker.Lib.RLTDBContext();
            logsRepo = new DBLinker.Lib.Repository.Impl.GenericRepository<RLT_LOG>();
            isSMSEnabled = ConfigurationManager.AppSettings["SmsEnable"].ToString();
        }


        #region 1 Add newBooking
        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public ActionResult NewBooking()
        {
            var CityList = from a in entity.RLT_CITY orderby a.City_Name where a.Is_Active == true select a;
            ViewBag.CityList = CityList;

            var TripTypeList = entity.RLT_TRIP_TYPES.ToList();
            ViewBag.TripTypeList = TripTypeList;

            var CarCategory = entity.RLT_CAR_CATEGORY.Where(x => x.Is_Active == true).ToList().Select(c => new { PKID = c.PKID, Car_Category = c.Car_Category_Abbr + " ( " + c.Per_KM_fare + " RS Per KM )" });
            ViewBag.CarCategory = CarCategory;

            var BookingStatusList = entity.RLT_BOOKING_STATUS.Where(x => x.Is_Active == true).ToList();
            ViewBag.BookingStatusList = BookingStatusList;

            var PaymentMethodList = entity.RLT_PAYMENT_METHOD.Where(x => x.Is_Active == true).ToList();
            ViewBag.PaymentMethodList = PaymentMethodList;

            // Debug: Log payment methods for troubleshooting
            System.Diagnostics.Debug.WriteLine($"Found {PaymentMethodList.Count} active payment methods");
            foreach (var pm in PaymentMethodList)
            {
                System.Diagnostics.Debug.WriteLine($"Payment Method: {pm.Payment_Method_Name} (ID: {pm.PKID})");
            }

            M_Booking m_Booking = new M_Booking();
            return View(m_Booking);

        }

        // Test endpoint to check payment methods
        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public JsonResult GetPaymentMethods()
        {
            var paymentMethods = entity.RLT_PAYMENT_METHOD.Where(x => x.Is_Active == true)
                .Select(x => new
                {
                    PKID = x.PKID,
                    Payment_Method_Name = x.Payment_Method_Name,
                    Is_Active = x.Is_Active
                }).ToList();

            return Json(paymentMethods, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("NewBooking", RoleBaseAuthentication.ActionType.Add)]
        public ActionResult NewBooking(M_Booking obj)
        {
            try
            {
                // Step 1: Check if user exists by mobile number, if not create user
                string userId = GetOrCreateUser(obj.Mobile_No1, obj.Name, obj.Mail_Id);

                if (string.IsNullOrEmpty(userId))
                {
                    return Json(new { success = false, message = "Failed to create or find user account" }, JsonRequestBehavior.AllowGet);
                }

                // Step 2: Create booking with all required fields from API structure
                obj.Booking_Status_Id = 3; // Pending
                obj.Is_Active = true;
                obj.Booking_Id = CreateBookingID();
                obj.IsAdminBooked = true; // Mark as admin created
                obj.Booking_Created_By = userId; // Store user ID as string

                var vdtls = map.Booking(obj);
                // Note: Created_By in RLT_BOOKING might be int, we'll store user reference in Booking_Created_By instead

                // Set additional fields to match API structure
                vdtls.BookedBy = "Admin";
                vdtls.PaymentType = obj.Is_OnlinePayment == 1 ? "FULL" : "CASH";

                // Handle payment logic based on correct PaymentOption enum values
                // PaymentOption enum: PartialPay=1, FullPay=2, FullPayToDriver=3

                // Debug logging to track payment option values
                System.Diagnostics.Debug.WriteLine($"NewBooking Debug - PaymentOption: {obj.PaymentOption}, Is_OnlinePayment: {obj.Is_OnlinePayment}, PartialPaymentAmount: {obj.PartialPaymentAmount}");

                // Enhanced logic to handle partial payments more robustly
                // Check multiple conditions to detect partial payments
                bool isPartialPayment = (obj.PaymentOption == 1) ||
                                       (obj.PartialPaymentAmount.HasValue && obj.PartialPaymentAmount.Value > 0);

                if (isPartialPayment && obj.Is_OnlinePayment == 1) // Partial payment (PartialPay=1)
                {
                    vdtls.PaymentType = "PARTIAL";
                    vdtls.PaymentOption = 1; // PartialPay = 1
                    vdtls.PartialPaymentAmount = obj.PartialPaymentAmount;

                    // Calculate remaining amount for driver: Total fare - Partial payment amount
                    decimal totalFare = obj.Fare ?? 0;
                    decimal partialAmount = obj.PartialPaymentAmount ?? 0;
                    decimal remainingAmount = totalFare - partialAmount;

                    vdtls.CashAmountToPayDriver = remainingAmount;
                    vdtls.RemainingAmountForDriver = remainingAmount;

                    System.Diagnostics.Debug.WriteLine($"Partial Payment - Total: {totalFare}, Partial: {partialAmount}, Remaining: {remainingAmount}");
                }
                else if (obj.Is_OnlinePayment == 1) // Full online payment (FullPay=2)
                {
                    vdtls.PaymentType = "FULL";
                    vdtls.PaymentOption = 2; // FullPay = 2
                    vdtls.RemainingAmountForDriver = 0;
                    vdtls.CashAmountToPayDriver = 0;
                }
                else // Cash payment (FullPayToDriver=3)
                {
                    vdtls.PaymentType = "CASH";
                    vdtls.PaymentOption = 3; // FullPayToDriver = 3
                    vdtls.CashAmountToPayDriver = obj.Fare;
                    vdtls.RemainingAmountForDriver = obj.Fare;
                }

                // Step 3: Generate secure payment signature for validation
                string paymentSignature = GeneratePaymentSignature(obj.Booking_Id, obj.Mobile_No1, obj.Fare.ToString(), vdtls.PaymentType);
                vdtls.razorpay_signature = paymentSignature; // Store security signature
                vdtls.razorpay_status = "Payment Pending"; // Set initial status

                // Step 4: Save booking first to get PKID
                entity.RLT_BOOKING.Add(vdtls);
                entity.SaveChanges();

                // Step 5: Generate frontend payment URL (only for online payments)
                string paymentUrl = "";
                if (obj.Is_OnlinePayment == 1)
                {
                    // Generate PhonePe transaction ID and store it in razorpay_order_id field
                    string transactionId = obj.Booking_Id + "_" + DateTime.Now.Ticks;
                    vdtls.razorpay_order_id = transactionId;

                    // Use URL-safe encrypted booking ID for security (prevents enumeration attacks)
                    // This method generates browser-compatible URLs without = padding issues
                    string urlSafePaymentId = QueryStringEncoding.EncryptForUrl(vdtls.Booking_Id.ToString(), "CabYaari@2024#AnonymousPayment!SecureKey");
                    paymentUrl = $"https://cabyaari.com/#/pay/?id={urlSafePaymentId}";
                    vdtls.payment_link = paymentUrl;
                    entity.SaveChanges();

                    // Step 6: Send SMS with payment link
                    if (isSMSEnabled == "true")
                    {
                        SendPaymentLinkSMS(obj, paymentUrl, vdtls);
                    }
                }
                else
                {
                    // For cash bookings, send confirmation SMS
                    if (isSMSEnabled == "true")
                    {
                        string smsMessage = $"Dear {obj.Name}, your booking {obj.Booking_Id} is confirmed. Payment: Cash on delivery.";
                        SendSMS(obj.Mobile_No1, "SMS", "BookingConfirm", smsMessage, "SMSReplaceStringBooking");
                    }
                }

                // Encrypt the booking ID for URL security (same as used in BookingList view)
                string encryptedBookingId = QueryStringEncoding.EncryptString(vdtls.PKID.ToString(), "Booking");

                // Return success with booking details
                return Json(new
                {
                    success = true,
                    bookingId = encryptedBookingId, // Return encrypted ID for URL
                    rawBookingId = vdtls.PKID, // Keep raw ID for reference if needed
                    bookingNumber = obj.Booking_Id,
                    paymentUrl = paymentUrl,
                    paymentType = vdtls.PaymentType,
                    message = obj.Is_OnlinePayment == 1 ? "Booking created successfully. Payment link sent to customer." : "Booking created successfully."
                }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                // Log the error to RLT_LOG table for debugging
                try
                {
                    var logEntry = new RLT_LOG
                    {
                        Date = DateTime.Now,
                        Thread = System.Threading.Thread.CurrentThread.ManagedThreadId.ToString(),
                        Level = "ERROR",
                        Logger = "BookingManagementController.NewBooking",
                        Message = $"Error creating new booking: {ex.Message}",
                        Exception = ex.ToString(),
                        CreatedDate = DateTime.Now,
                        CreatedBy = 1 // Admin user ID, you can get actual user ID from session if needed
                    };

                    logsRepo.Add(logEntry);
                    logsRepo.Save();
                }
                catch (Exception logEx)
                {
                    // If logging fails, at least write to debug output
                    System.Diagnostics.Debug.WriteLine($"Failed to log error: {logEx.Message}");
                    System.Diagnostics.Debug.WriteLine($"Original error: {ex.Message}");
                }

                return Json(new { success = false, message = ex.Message }, JsonRequestBehavior.AllowGet);
            }
        }

        // Replace usage of QueryFirstOrDefault with manual SqlCommand and SqlDataReader logic in GetOrCreateUser and GetBasicRoleId

        private string GetOrCreateUser(string mobileNumber, string name, string email)
        {
            try
            {
                string checkUserQuery = @"
                    SELECT Id FROM [Identity].[User]
                    WHERE PhoneNumber = @PhoneNumber";

                using (var connection = new System.Data.SqlClient.SqlConnection(
                    System.Configuration.ConfigurationManager.ConnectionStrings["RLTDBconnection"].ConnectionString))
                {
                    connection.Open();

                    string existingUserId = null;
                    using (var cmd = new System.Data.SqlClient.SqlCommand(checkUserQuery, connection))
                    {
                        cmd.Parameters.AddWithValue("@PhoneNumber", mobileNumber);
                        using (var reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                existingUserId = reader["Id"].ToString();
                            }
                        }
                    }

                    if (!string.IsNullOrEmpty(existingUserId))
                    {
                        return existingUserId;
                    }

                    string[] nameParts = name?.Split(' ') ?? new[] { "", "" };
                    string firstName = nameParts.Length > 0 ? nameParts[0] : "";
                    string lastName = nameParts.Length > 1 ? string.Join(" ", nameParts.Skip(1)) : "";

                    string newUserId = Guid.NewGuid().ToString();
                    string insertUserQuery = @"
                        INSERT INTO [Identity].[User]
                        (Id, UserName, NormalizedUserName, Email, NormalizedEmail, EmailConfirmed,
                         PhoneNumber, PhoneNumberConfirmed, TwoFactorEnabled, LockoutEnabled,
                         AccessFailedCount, FirstName, LastName, UserType, SecurityStamp, ConcurrencyStamp, OTPExpireTimeInMinute)
                        VALUES
                        (@Id, @UserName, @NormalizedUserName, @Email, @NormalizedEmail, @EmailConfirmed,
                         @PhoneNumber, @PhoneNumberConfirmed, @TwoFactorEnabled, @LockoutEnabled,
                         @AccessFailedCount, @FirstName, @LastName, @UserType, @SecurityStamp, @ConcurrencyStamp, @OTPExpireTimeInMinute)";

                    using (var cmd = new System.Data.SqlClient.SqlCommand(insertUserQuery, connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", newUserId);
                        cmd.Parameters.AddWithValue("@UserName", mobileNumber);
                        cmd.Parameters.AddWithValue("@NormalizedUserName", mobileNumber.ToUpper());
                        cmd.Parameters.AddWithValue("@Email", (object)email ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@NormalizedEmail", (object)(email?.ToUpper()) ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@EmailConfirmed", false);
                        cmd.Parameters.AddWithValue("@PhoneNumber", mobileNumber);
                        cmd.Parameters.AddWithValue("@PhoneNumberConfirmed", false);
                        cmd.Parameters.AddWithValue("@TwoFactorEnabled", false);
                        cmd.Parameters.AddWithValue("@LockoutEnabled", true);
                        cmd.Parameters.AddWithValue("@AccessFailedCount", 0);
                        cmd.Parameters.AddWithValue("@FirstName", firstName);
                        cmd.Parameters.AddWithValue("@LastName", lastName);
                        cmd.Parameters.AddWithValue("@UserType", 2);
                        cmd.Parameters.AddWithValue("@SecurityStamp", Guid.NewGuid().ToString());
                        cmd.Parameters.AddWithValue("@ConcurrencyStamp", Guid.NewGuid().ToString());
                        cmd.Parameters.AddWithValue("@OTPExpireTimeInMinute", 5); // Default 5 minutes for OTP expiry
                        cmd.ExecuteNonQuery();
                    }

                    string roleId = GetBasicRoleId(connection);
                    if (!string.IsNullOrEmpty(roleId))
                    {
                        string insertUserRoleQuery = @"
                            INSERT INTO [Identity].[UserRoles] (UserId, RoleId)
                            VALUES (@UserId, @RoleId)";
                        using (var cmd = new System.Data.SqlClient.SqlCommand(insertUserRoleQuery, connection))
                        {
                            cmd.Parameters.AddWithValue("@UserId", newUserId);
                            cmd.Parameters.AddWithValue("@RoleId", roleId);
                            cmd.ExecuteNonQuery();
                        }
                    }

                    return newUserId;
                }
            }
            catch (Exception ex)
            {
                // Log the error to RLT_LOG table for debugging
                try
                {
                    var logEntry = new RLT_LOG
                    {
                        Date = DateTime.Now,
                        Thread = System.Threading.Thread.CurrentThread.ManagedThreadId.ToString(),
                        Level = "ERROR",
                        Logger = "BookingManagementController.GetOrCreateUser",
                        Message = $"Error creating/finding user for mobile: {mobileNumber}. Error: {ex.Message}",
                        Exception = ex.ToString(),
                        CreatedDate = DateTime.Now,
                        CreatedBy = 1 // Admin user ID
                    };

                    logsRepo.Add(logEntry);
                    logsRepo.Save();
                }
                catch (Exception logEx)
                {
                    // If logging fails, at least write to debug output
                    System.Diagnostics.Debug.WriteLine($"Failed to log GetOrCreateUser error: {logEx.Message}");
                }

                System.Diagnostics.Debug.WriteLine("Error creating user: " + ex.Message);
                return "";
            }
        }

        // Helper method to get Basic role ID
        private string GetBasicRoleId(System.Data.SqlClient.SqlConnection connection)
        {
            try
            {
                string getRoleQuery = "SELECT Id FROM [Identity].[Role] WHERE Name = 'Basic'";
                using (var cmd = new System.Data.SqlClient.SqlCommand(getRoleQuery, connection))
                {
                    using (var reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return reader["Id"].ToString();
                        }
                    }
                }
                return "";
            }
            catch
            {
                return "";
            }
        }

        // Helper method to log errors to RLT_LOG table
        private void LogError(string methodName, string message, Exception ex)
        {
            try
            {
                var logEntry = new RLT_LOG
                {
                    Date = DateTime.Now,
                    Thread = System.Threading.Thread.CurrentThread.ManagedThreadId.ToString(),
                    Level = "ERROR",
                    Logger = $"BookingManagementController.{methodName}",
                    Message = message,
                    Exception = ex.ToString(),
                    CreatedDate = DateTime.Now,
                    CreatedBy = 1 // Admin user ID, you can get actual user ID from session if needed
                };

                logsRepo.Add(logEntry);
                logsRepo.Save();
            }
            catch (Exception logEx)
            {
                // If logging fails, at least write to debug output
                System.Diagnostics.Debug.WriteLine($"Failed to log error in {methodName}: {logEx.Message}");
                System.Diagnostics.Debug.WriteLine($"Original error: {ex.Message}");
            }
        }

        // Generate secure payment signature for validation
        private string GeneratePaymentSignature(string bookingId, string mobileNumber, string amount, string paymentType)
        {
            string secretKey = ConfigurationManager.AppSettings["PaymentSecretKey"] ?? "CabYaari_Payment_Secret_Key_2024_Secure";
            string dataToSign = $"{bookingId}|{mobileNumber}|{amount}|{paymentType}|{DateTime.Now:yyyyMMdd}";

            using (var hmac = new System.Security.Cryptography.HMACSHA256(Encoding.UTF8.GetBytes(secretKey)))
            {
                byte[] hashBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(dataToSign));
                return Convert.ToBase64String(hashBytes);
            }
        }

        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public string CreateBookingID()
        {
            Random generator = new Random();
            String r = "CY-" + DateTime.Now.ToString("dd") + DateTime.Now.ToString("MM") + DateTime.Now.ToString("yy") + "-" + generator.Next(0, 99999).ToString("D5");
            return r;
        }



        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public JsonResult calculateFare(int City_From_Id = 0, int City_To_Id = 0, int Trip_Type_Id = 0, int Car_Category_Id = 0)
        {
            var FareChart = (from a in entity.RLT_BOOKING_FARE
                             where a.City_From == City_From_Id && a.City_To == City_To_Id
                            & a.Trip_Type_Id == Trip_Type_Id && a.Car_Category_Id == Car_Category_Id
                             select new
                             {
                                 a.Final_Fare,
                                 a.GST_Amount,
                                 a.Basic_Fare,
                                 a.Driver_Charge,
                                 a.Toll_Charge,
                                 a.GST
                             }).SingleOrDefault();
            if (FareChart != null)
                return Json(FareChart, JsonRequestBehavior.AllowGet);
            else
            {
                return Json("", JsonRequestBehavior.AllowGet);
            }
        }

        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public JsonResult calculateFareBasedOnPickupDropOffAddress(int City_From_Id = 0, int City_To_Id = 0, int Trip_Type_Id = 0, int Car_Category_Id = 0)
        {
            var FareChart = (from a in entity.RLT_BOOKING_FARE
                             where a.City_From == City_From_Id && a.City_To == City_To_Id
                            & a.Trip_Type_Id == Trip_Type_Id && a.Car_Category_Id == Car_Category_Id
                             select new
                             {
                                 a.Final_Fare,
                                 a.GST_Amount,
                                 a.Basic_Fare,
                                 a.Driver_Charge,
                                 a.Toll_Charge,
                                 a.GST
                             }).SingleOrDefault();
            if (FareChart != null)
                return Json(FareChart, JsonRequestBehavior.AllowGet);
            else
            {
                return Json("", JsonRequestBehavior.AllowGet);
            }
        }

        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public JsonResult calculateDiscount(string Coupon_Code = "")
        {
            var CouponData = (from a in entity.RLT_DISCOUNT_COUPON
                              where a.Discount_Coupon == Coupon_Code
                              select new
                              {
                                  Discount = a.Discount,
                                  Max_Discount = a.Max_Discount,
                                  Fare_When_Applied = a.Fare_When_Applied,
                                  Coupon_Last_Date = a.Coupon_Last_Date,
                                  Is_Active = a.Is_Active
                              }).FirstOrDefault();




            if (CouponData != null)
            {
                bool Is_Active = true;
                if (CouponData.Coupon_Last_Date < DateTime.Now)
                    Is_Active = false;


                var CouponDataNew = new
                {
                    Discount = CouponData.Discount,
                    Max_Discount = CouponData.Max_Discount,
                    Fare_When_Applied = CouponData.Fare_When_Applied,
                    Is_Active = Is_Active
                };


                return Json(CouponDataNew, JsonRequestBehavior.AllowGet);
            }
            else
            {
                return Json("", JsonRequestBehavior.AllowGet);
            }

        }

        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public string CreatePhonePePaymentLink(M_Booking obj)
        {
            try
            {
                // Get PhonePe configuration from web.config
                string merchantId = ConfigurationManager.AppSettings["PhonePe_MerchantId"];
                string saltKey = ConfigurationManager.AppSettings["PhonePe_SaltKey"];
                string saltIndex = ConfigurationManager.AppSettings["PhonePe_SaltIndex"];
                string baseUrl = ConfigurationManager.AppSettings["PhonePe_BaseUrl"];
                string callbackUrl = ConfigurationManager.AppSettings["PhonePe_CallbackUrl"];

                // Create unique transaction ID
                string transactionId = obj.Booking_Id + "_" + DateTime.Now.Ticks;

                // Convert fare to paise (multiply by 100)
                int amountInPaise = Convert.ToInt32(obj.Fare * 100);

                // Create payment request payload
                var paymentRequest = new
                {
                    merchantId = merchantId,
                    transactionId = transactionId,
                    merchantOrderId = obj.Booking_Id,
                    amount = amountInPaise,
                    mobileNumber = obj.Mobile_No1,
                    message = $"Payment for booking {obj.Booking_Id}",
                    expiresIn = 3600, // 1 hour expiry
                    shortName = obj.Name,
                    subMerchantId = "CabYaari"
                };

                // Convert to JSON and encode to Base64
                string jsonPayload = JsonConvert.SerializeObject(paymentRequest);
                string base64Payload = Convert.ToBase64String(Encoding.UTF8.GetBytes(jsonPayload));

                // Create X-VERIFY header
                string endpoint = "/v3/payLink/init";
                string stringToHash = base64Payload + endpoint + saltKey;
                string xVerify = ComputeSHA256Hash(stringToHash) + "###" + saltIndex;

                // Make API call to PhonePe
                using (var client = new WebClient())
                {
                    client.Headers.Add("Content-Type", "application/json");
                    client.Headers.Add("X-VERIFY", xVerify);

                    var requestBody = new { request = base64Payload };
                    string requestJson = JsonConvert.SerializeObject(requestBody);

                    string apiUrl = baseUrl + endpoint;
                    string response = client.UploadString(apiUrl, "POST", requestJson);

                    // Parse response
                    dynamic responseObj = JsonConvert.DeserializeObject(response);

                    if (responseObj.success == true && responseObj.data != null)
                    {
                        return responseObj.data.payLink.ToString();
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("PhonePe API Error: " + response);
                        return "Error: " + (responseObj.message ?? "Unknown error");
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error to RLT_LOG table for debugging
                try
                {
                    var logEntry = new RLT_LOG
                    {
                        Date = DateTime.Now,
                        Thread = System.Threading.Thread.CurrentThread.ManagedThreadId.ToString(),
                        Level = "ERROR",
                        Logger = "BookingManagementController.CreatePhonePePaymentLink",
                        Message = $"Error creating PhonePe payment link for booking {obj.Booking_Id}: {ex.Message}",
                        Exception = ex.ToString(),
                        CreatedDate = DateTime.Now,
                        CreatedBy = 1 // Admin user ID
                    };

                    logsRepo.Add(logEntry);
                    logsRepo.Save();
                }
                catch (Exception logEx)
                {
                    // If logging fails, at least write to debug output
                    System.Diagnostics.Debug.WriteLine($"Failed to log PhonePe payment link error: {logEx.Message}");
                }

                System.Diagnostics.Debug.WriteLine("PhonePe Payment Link Error: " + ex.Message);
                return "Error generating payment link: " + ex.Message;
            }
        }

        // Helper method to compute SHA256 hash
        private string ComputeSHA256Hash(string input)
        {
            using (SHA256 sha256Hash = SHA256.Create())
            {
                byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(input));
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }

        // PhonePe callback method to handle payment responses
        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public ActionResult PhonePeCallback()
        {
            try
            {
                // Get the response from PhonePe
                string response = Request.Form["response"];

                if (!string.IsNullOrEmpty(response))
                {
                    // Decode the base64 response
                    string decodedResponse = Encoding.UTF8.GetString(Convert.FromBase64String(response));
                    dynamic responseObj = JsonConvert.DeserializeObject(decodedResponse);

                    string transactionId = responseObj.data.transactionId;
                    string status = responseObj.data.state;

                    // Extract booking ID from transaction ID
                    string bookingId = transactionId.Split('_')[0];

                    // Update booking status in database
                    var booking = entity.RLT_BOOKING.FirstOrDefault(b => b.Booking_Id == bookingId);
                    if (booking != null)
                    {
                        if (status == "COMPLETED")
                        {
                            booking.razorpay_status = "Success";
                            booking.razorpay_payment_id = responseObj.data.transactionId;
                        }
                        else
                        {
                            booking.razorpay_status = "Failed";
                        }
                        entity.SaveChanges();
                    }

                    // Redirect to appropriate page based on status
                    if (status == "COMPLETED")
                    {
                        return RedirectToAction("PaymentSuccess", new { bookingId = bookingId });
                    }
                    else
                    {
                        return RedirectToAction("PaymentFailed", new { bookingId = bookingId });
                    }
                }

                return RedirectToAction("BookingList");
            }
            catch (Exception ex)
            {
                // Log the error to RLT_LOG table for debugging
                try
                {
                    var logEntry = new RLT_LOG
                    {
                        Date = DateTime.Now,
                        Thread = System.Threading.Thread.CurrentThread.ManagedThreadId.ToString(),
                        Level = "ERROR",
                        Logger = "BookingManagementController.PhonePeCallback",
                        Message = $"Error processing PhonePe callback: {ex.Message}",
                        Exception = ex.ToString(),
                        CreatedDate = DateTime.Now,
                        CreatedBy = 1 // Admin user ID
                    };

                    logsRepo.Add(logEntry);
                    logsRepo.Save();
                }
                catch (Exception logEx)
                {
                    // If logging fails, at least write to debug output
                    System.Diagnostics.Debug.WriteLine($"Failed to log PhonePe callback error: {logEx.Message}");
                }

                System.Diagnostics.Debug.WriteLine("PhonePe Callback Error: " + ex.Message);
                return RedirectToAction("BookingList");
            }
        }

        // Payment success page
        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public ActionResult PaymentSuccess(string bookingId)
        {
            ViewBag.BookingId = bookingId;
            ViewBag.Message = "Payment completed successfully!";
            return View();
        }

        // Payment failed page
        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public ActionResult PaymentFailed(string bookingId)
        {
            ViewBag.BookingId = bookingId;
            ViewBag.Message = "Payment failed. Please try again.";
            return View();
        }

        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public string CreateRazorPayorderId(M_Booking obj)
        {
            int Amount = Convert.ToInt32(obj.Fare) * 100;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            Dictionary<string, object> input = new Dictionary<string, object>();
            input.Add("amount", Amount); // this amount should be same as transaction amount
            input.Add("currency", "INR");
            input.Add("receipt", obj.Booking_Id);
            input.Add("payment_capture", 1);
            //string key = "rzp_test_xig2LY8ipxmNYl";
            //string secret = "2gxZBmAxeFpkzxSc63MhR98m";
            string key = "***********************";
            string secret = "6C3ePMku6mmolqbuoNMK0G6s";
            RazorpayClient client = new RazorpayClient(key, secret);
            Razorpay.Api.Order order = client.Order.Create(input);
            string orderId = order["id"].ToString();


            /* Prepare HTML Form and Submit to Paytm */
            String outputHtml = "";
            outputHtml += "<html>";
            outputHtml += "<head>";
            outputHtml += "<title>Cab Yari Payment</title>";
            outputHtml += "</head>";
            outputHtml += "<body>";
            outputHtml += "<form method='post' action='http://mypanel.revueleague.com/BookingManagement/RazorPayPayment_Success'>";
            outputHtml += "<script src ='https://checkout.razorpay.com/v1/checkout.js'";
            outputHtml += " data-key = '***********************'";
            outputHtml += " data-amount = " + Amount + "";
            outputHtml += " data-name ='Razorpay'";
            outputHtml += " data-description ='Cab Booking'";
            outputHtml += " data-order_id = " + orderId + "";
            outputHtml += " data-image ='https://razorpay.com/favicon.png'";
            outputHtml += " data-prefill.name =" + obj.Name + "";
            outputHtml += " data-prefill.email =" + obj.Mail_Id + "";
            outputHtml += " data-prefill.contact =" + obj.Mobile_No1 + "";
            outputHtml += " data-theme.color ='#F37254'";
            outputHtml += " ></script>";
            outputHtml += "<input type='hidden' name='hidden' value='Hidden Element'>";
            outputHtml += "</form></body></html>";

            TempData["RazorList"] = outputHtml;
            return orderId;
        }

        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public ActionResult RazorPayPayment()
        {
            return View("RazorPayPayment");
        }

        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public ActionResult RazorPayPayment_Success()
        {
            string paymentId = Request.Form["razorpay_payment_id"];
            string razorpay_order_id = Request.Form["razorpay_order_id"];
            if (paymentId != null)
            {

                string key = "***********************";
                string secret = "6C3ePMku6mmolqbuoNMK0G6s";

                RazorpayClient client = new RazorpayClient(key, secret);

                Dictionary<string, string> attributes = new Dictionary<string, string>();

                attributes.Add("razorpay_payment_id", paymentId);
                attributes.Add("razorpay_order_id", Request.Form["razorpay_order_id"]);
                attributes.Add("razorpay_signature", Request.Form["razorpay_signature"]);
                Utils.verifyPaymentSignature(attributes);

                var Booking = from a in entity.RLT_BOOKING where a.razorpay_order_id == razorpay_order_id select a;
                foreach (var a in Booking)
                {
                    a.razorpay_status = "Success";
                    a.razorpay_payment_id = Request.Form["razorpay_order_id"];
                    a.razorpay_signature = Request.Form["razorpay_signature"];
                }
            }

            return View();
        }
        #endregion



        #region 2 View / Edit Booking List
        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public ActionResult BookingList()
        {
            var CityList = entity.RLT_CITY.ToList();
            ViewBag.CityList = CityList;

            var TripTypeList = entity.RLT_TRIP_TYPES.ToList();
            ViewBag.TripTypeList = TripTypeList;

            var CarCategory = entity.RLT_CAR_CATEGORY.ToList().Select(c => new { PKID = c.PKID, Car_Category = c.Car_Category_Abbr + " (" + c.Car_Category_Name + ")" });
            ViewBag.CarCategory = CarCategory;

            var BookingStatusList = entity.RLT_BOOKING_STATUS.ToList();
            ViewBag.BookingStatusList = BookingStatusList;

            var PaymentMethodList = entity.RLT_PAYMENT_METHOD.ToList();
            ViewBag.PaymentMethodList = PaymentMethodList;

            var vendorList = entity.RLT_Vendor_Details.ToList();
            ViewBag.vendorList = vendorList;

            var BookingList = (from a in entity.RLT_BOOKING
                               join b in entity.RLT_CITY on a.City_From_Id equals b.PKID
                               join c in entity.RLT_CITY on a.City_To_Id equals c.PKID
                               join d in entity.RLT_TRIP_TYPES on a.Trip_Type_Id equals d.PKID

                               join e in entity.RLT_CAR_CATEGORY on a.Car_Category_Id equals e.PKID into e1
                               from e in e1.DefaultIfEmpty()

                               join f in entity.RLT_BOOKING_STATUS on a.Booking_Status_Id equals f.PKID into f1
                               from f in f1.DefaultIfEmpty()

                               // Skip Is_Active check - show all bookings regardless of active status
                               orderby a.PKID descending
                               select new M_Booking
                               {
                                   PKID = a.PKID,
                                   Booking_Id = a.Booking_Id,
                                   City_From_Id = a.City_From_Id,
                                   City_From = b.City_Name,
                                   City_To = c.City_Name,
                                   City_To_Id = a.City_To_Id,
                                   Trip_Type = d.Trip_Type,
                                   Trip_Type_Id = a.Trip_Type_Id,
                                   Car_Category_Id = a.Car_Category_Id,
                                   Car_Category = e != null ? e.Car_Category_Abbr : "N/A",

                                   Basic_Fare = a.Basic_Fare,
                                   Driver_Charge = a.Driver_Charge,
                                   Toll_Charge = a.Toll_Charge,
                                   GST = a.GST,
                                   Distance = a.Distance,
                                   Duration = a.Duration,
                                   GST_Fare = a.GST_Fare,
                                   Fare = a.Fare,
                                   PickUp_Address = a.PickUp_Address,
                                   DropOff_Address = a.DropOff_Address,
                                   PickUp_Date = a.PickUp_Date,
                                   Booking_Date = a.Booking_Date,
                                   PickUp_Time = a.PickUp_Time,
                                   Name = a.Name,
                                   Mobile_No1 = a.Mobile_No1,
                                   Mobile_No2 = a.Mobile_No2,
                                   Mail_Id = a.Mail_Id,
                                   Mode_Of_Payment_Id = a.Mode_Of_Payment_Id,
                                   Booking_Status = f != null ? f.BOOKING_STATUS : "Unknown",
                                   Vendor_PKID = a.Vendor_Id,
                                   Car_PKID = a.Car_Id,
                                   Booking_Status_Id = f != null ? f.PKID : a.Booking_Status_Id ?? 0,
                                   BookingEditRemark = a.BookingEditRemark,
                                   Booking_Remark = a.Booking_Remark,
                                   Is_Active = a.Is_Active ?? true, // Default to true if null
                                   PickUpAddressLatitude = a.PickUpAddressLatitude,
                                   PickUpAddressLongitude = a.PickUpAddressLongitude,
                                   completepickupaddress = a.completepickupaddress,
                                   completedropoffpaddress = a.completedropoffpaddress
                               }).ToList();

            ViewBag.BookingList = BookingList;

            M_Booking m_Booking = new M_Booking();
            return View(m_Booking);
        }

        [RoleBaseAuthentication("BookingList", RoleBaseAuthentication.ActionType.Edit)]
        public ActionResult UpdateBooking(M_Booking obj)
        {
            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                var vdtlsList = (from a in entity.RLT_BOOKING where a.PKID == obj.PKID select a);
                foreach (RLT_BOOKING vdtls in vdtlsList)
                {

                    vdtls.City_From_Id = obj.City_From_Id;
                    vdtls.City_To_Id = obj.City_To_Id;
                    vdtls.Trip_Type_Id = obj.Trip_Type_Id;
                    vdtls.Car_Category_Id = obj.Car_Category_Id;
                    vdtls.Distance = obj.Distance;
                    vdtls.PickUp_Date = obj.PickUp_Date;
                    vdtls.PickUp_Time = obj.PickUp_Time;
                    vdtls.Booking_Remark = obj.Booking_Remark;
                    vdtls.Basic_Fare = obj.Basic_Fare;
                    vdtls.Driver_Charge = obj.Driver_Charge;
                    vdtls.Toll_Charge = obj.Toll_Charge;
                    vdtls.GST = obj.GST;
                    vdtls.GST_Fare = obj.GST_Fare;
                    vdtls.Fare = obj.Fare;
                    vdtls.PickUp_Address = obj.PickUp_Address;
                    vdtls.DropOff_Address = obj.DropOff_Address;
                    vdtls.PickUp_Date = obj.PickUp_Date;
                    vdtls.PickUp_Time = obj.PickUp_Time;
                    vdtls.Name = obj.Name;
                    vdtls.Mail_Id = obj.Mail_Id;
                    vdtls.Mobile_No1 = obj.Mobile_No1;
                    vdtls.Mobile_No2 = obj.Mobile_No2;
                    vdtls.Mode_Of_Payment_Id = obj.Mode_Of_Payment_Id;
                    vdtls.Is_Active = obj.Is_Active;
                    vdtls.BookingEditRemark = obj.BookingEditRemark;
                    vdtls.Updated_Date = DateTime.Now;
                    vdtls.driver_id = obj.driver_id;
                    vdtls.PickUpAddressLatitude = obj.PickUpAddressLatitude;
                    vdtls.PickUpAddressLongitude = obj.PickUpAddressLongitude;
                    vdtls.completepickupaddress = obj.completepickupaddress;
                    vdtls.completedropoffpaddress = obj.completedropoffpaddress;

                }
                entity.SaveChanges();
                Id = 2;
            }
            if (Request.Form["hdnOperation"].Equals("E"))
            {
                var vdtlsList = (from a in entity.RLT_BOOKING where a.PKID == obj.PKID select a);
                foreach (RLT_BOOKING vdtls in vdtlsList)
                {
                    vdtls.Is_Active = obj.Is_Active;
                }
                entity.SaveChanges();
                Id = 3;
            }

            return Json(Id, JsonRequestBehavior.AllowGet);
        }
        #endregion


        #region 3 View Booking one by one
        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public ActionResult ViewBooking()
        {
            M_Booking Bookingr_Details = new M_Booking();
            if (Request.QueryString["id"] != null)
            {
                try
                {
                    string idParam = Convert.ToString(Request.QueryString["id"]);
                    int BookingId;

                    // Try to decrypt the ID first (for encrypted URLs)
                    try
                    {
                        string DecData = QueryStringEncoding.DecryptString(idParam, "Booking");
                        BookingId = Convert.ToInt32(DecData);
                    }
                    catch (FormatException)
                    {
                        // If decryption fails, try to parse as plain integer
                        if (!int.TryParse(idParam, out BookingId))
                        {
                            // If both fail, redirect to booking list
                            return RedirectToAction("BookingList");
                        }
                    }

                    Bookingr_Details = (from a in entity.RLT_BOOKING
                                        join b in entity.RLT_CITY on a.City_From_Id equals b.PKID // INNER JOIN
                                        join c in entity.RLT_CITY on a.City_To_Id equals c.PKID   // INNER JOIN
                                        join d in entity.RLT_TRIP_TYPES on a.Trip_Type_Id equals d.PKID // INNER JOIN

                                        join e in entity.RLT_CAR_CATEGORY on a.Car_Category_Id equals e.PKID into e1 // LEFT JOIN
                                        from e in e1.DefaultIfEmpty()

                                        join f in entity.RLT_PAYMENT_METHOD on a.Mode_Of_Payment_Id equals f.PKID into f1 // LEFT JOIN
                                        from f in f1.DefaultIfEmpty()

                                        join g in entity.RLT_Vendor_Details on a.Vendor_Id equals g.PKID into g1 // LEFT JOIN
                                        from g in g1.DefaultIfEmpty()

                                        join h in entity.RLT_CAR_DETAILS on a.Car_Id equals h.PKID into h1 // LEFT JOIN
                                        from h in h1.DefaultIfEmpty()

                                        join i in entity.RLT_CAR_DRIVER_DETAILS on h.PKID equals i.Car_PKID into i1 // LEFT JOIN
                                        from i in i1.DefaultIfEmpty()

                                        where a.PKID == BookingId
                                        select new M_Booking
                                        {
                                            City_From = b.City_Name,
                                            City_To = c.City_Name,
                                            Trip_Type = d.Trip_Type,
                                            Car_Category = e.Car_Category_Abbr,
                                            Booking_Id = a.Booking_Id,
                                            Basic_Fare = a.Basic_Fare,
                                            GST = a.GST,
                                            Fare = a.Fare,
                                            Distance = a.Distance,
                                            Coupon_Code = a.Coupon_Code,
                                            PickUp_Address = a.PickUp_Address,
                                            DropOff_Address = a.DropOff_Address,
                                            PickUp_Date = a.PickUp_Date,
                                            PickUp_Time = a.PickUp_Time,
                                            Name = a.Name,
                                            Mobile_No1 = a.Mobile_No1,
                                            Mobile_No2 = a.Mobile_No2,
                                            Mail_Id = a.Mail_Id,
                                            Mode_Of_Payment = f.Payment_Method_Name,
                                            Vendor_Name = g.Vendor_Company_Name,
                                            Car_Number = h.Car_Number,
                                            Driver_Name = i.Driver_Name,
                                            Driver_Number = i.Drive_Phone_1,
                                            Booking_Remark = a.Booking_Remark,
                                            BookingEditRemark = a.BookingEditRemark,
                                            completepickupaddress = a.completepickupaddress,
                                            completedropoffpaddress = a.completedropoffpaddress,
                                            PaymentOption = a.PaymentOption,
                                            PaymentType = a.PaymentType, // Add PaymentType field
                                            PartialPaymentAmount = a.PartialPaymentAmount,
                                            RemainingAmountForDriver = a.RemainingAmountForDriver,
                                            CashAmountToPayDriver = a.CashAmountToPayDriver,
                                            payment_link = a.payment_link, // Dedicated payment link field
                                            razorpay_status = a.razorpay_status, // Payment status
                                            IsAdminBooked = a.IsAdminBooked
                                        }).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    // Log the error to RLT_LOG table for debugging
                    try
                    {
                        var logEntry = new RLT_LOG
                        {
                            Date = DateTime.Now,
                            Thread = System.Threading.Thread.CurrentThread.ManagedThreadId.ToString(),
                            Level = "ERROR",
                            Logger = "BookingManagementController.ViewBooking",
                            Message = $"Error viewing booking: {ex.Message}",
                            Exception = ex.ToString(),
                            CreatedDate = DateTime.Now,
                            CreatedBy = 1 // Admin user ID
                        };

                        logsRepo.Add(logEntry);
                        logsRepo.Save();
                    }
                    catch (Exception logEx)
                    {
                        // If logging fails, at least write to debug output
                        System.Diagnostics.Debug.WriteLine($"Failed to log ViewBooking error: {logEx.Message}");
                    }

                    // Log the error and redirect to booking list
                    System.Diagnostics.Debug.WriteLine("Error in ViewBooking: " + ex.Message);
                    return RedirectToAction("BookingList");
                }
            }
            if (Bookingr_Details != null)
            {
                return View(Bookingr_Details);
            }
            else
            {
                return View(new M_Booking());
            }

        }
        #endregion


        #region 4 Assign Cab
        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public ActionResult AssignCab()
        {
            var BookingStatusList = entity.RLT_BOOKING_STATUS.ToList();
            ViewBag.BookingStatusList = BookingStatusList;

            var vendorList = entity.RLT_Vendor_Details.ToList();
            ViewBag.vendorList = vendorList;

            M_Booking Bookingr_Details = new M_Booking();
            if (Request.QueryString["id"] != null)
            {
                try
                {
                    string idParam = Convert.ToString(Request.QueryString["id"]);
                    int BookingId;

                    // Try to decrypt the ID first (for encrypted URLs)
                    try
                    {
                        string DecData = QueryStringEncoding.DecryptString(idParam, "Booking");
                        BookingId = Convert.ToInt32(DecData);
                    }
                    catch (FormatException)
                    {
                        // If decryption fails, try to parse as plain integer
                        if (!int.TryParse(idParam, out BookingId))
                        {
                            // If both fail, redirect to booking list
                            return RedirectToAction("BookingList");
                        }
                    }

                    Bookingr_Details = (from a in entity.RLT_BOOKING
                                        where a.PKID == BookingId
                                        select new M_Booking
                                        {
                                            Booking_Id = a.Booking_Id,
                                            PKID = a.PKID,

                                        }).FirstOrDefault();
                }
                catch (Exception ex)
                {
                    // Log the error to RLT_LOG table for debugging
                    try
                    {
                        var logEntry = new RLT_LOG
                        {
                            Date = DateTime.Now,
                            Thread = System.Threading.Thread.CurrentThread.ManagedThreadId.ToString(),
                            Level = "ERROR",
                            Logger = "BookingManagementController.AssignCab",
                            Message = $"Error in AssignCab: {ex.Message}",
                            Exception = ex.ToString(),
                            CreatedDate = DateTime.Now,
                            CreatedBy = 1 // Admin user ID
                        };

                        logsRepo.Add(logEntry);
                        logsRepo.Save();
                    }
                    catch (Exception logEx)
                    {
                        // If logging fails, at least write to debug output
                        System.Diagnostics.Debug.WriteLine($"Failed to log AssignCab error: {logEx.Message}");
                    }

                    // Log the error and redirect to booking list
                    System.Diagnostics.Debug.WriteLine("Error in AssignCab: " + ex.Message);
                    return RedirectToAction("BookingList");
                }
            }
            return View(Bookingr_Details);

        }
        [HttpPost]
        [RoleBaseAuthentication("BookingList", RoleBaseAuthentication.ActionType.Edit)]
        public ActionResult AssignCab(M_Booking obj)
        {
            try
            {
                string InvoiceNo = "";
                if (obj.Booking_Status_Id == 6)
                    InvoiceNo = GetInvoiceNo();
                var bookingDetailsList = (from a in entity.RLT_BOOKING where a.PKID == obj.PKID select a);
                if (bookingDetailsList == null || bookingDetailsList.Count() == 0)
                    return Json("Booking not found", JsonRequestBehavior.AllowGet);

                foreach (RLT_BOOKING vdtls in bookingDetailsList)
                {
                    vdtls.Booking_Status_Id = obj.Booking_Status_Id;
                    vdtls.Updated_Date = DateTime.Now;

                    if (obj.Booking_Status_Id == 2)
                    {
                        vdtls.Vendor_Id = obj.Vendor_PKID;
                        vdtls.Car_Id = obj.Car_PKID;
                    }
                    if (obj.Booking_Status_Id == 4 || obj.Booking_Status_Id == 5)
                    {
                        vdtls.Booking_Remark = obj.Booking_Remark;
                    }
                    if (obj.Booking_Status_Id == 6) //Trip Completed
                    {
                        vdtls.Booking_Remark = obj.Booking_Remark;
                        vdtls.Invoice_No = InvoiceNo;
                        vdtls.Invoice_Date = DateTime.Now.Date;
                    }

                }
                entity.SaveChanges();



                var bookingDetails = bookingDetailsList.FirstOrDefault();
                if (bookingDetails == null)
                    return Json("Booking not found", JsonRequestBehavior.AllowGet);

                var carCategoryPPKM = (from a in entity.RLT_CAR_CATEGORY where a.PKID == bookingDetails.Car_Category_Id select a).FirstOrDefault();
                if (carCategoryPPKM == null)
                    return Json("Car category not found", JsonRequestBehavior.AllowGet);

                var CarDetails = (from a in entity.RLT_CAR_DETAILS where a.PKID == bookingDetails.Car_Id select a).FirstOrDefault();
                if (CarDetails == null)
                    return Json("Car details not found", JsonRequestBehavior.AllowGet);

                var DriverDetails = (from a in entity.RLT_CAR_DRIVER_DETAILS where a.PKID == bookingDetails.Car_Id select a).FirstOrDefault();
                if (DriverDetails == null)
                    return Json("Driver details not found", JsonRequestBehavior.AllowGet);



                if (isSMSEnabled == "true")
                {

                    var cityMap = entity.RLT_CITY
                        .Where(c => c.PKID == bookingDetails.City_From_Id || c.PKID == bookingDetails.City_To_Id)
                        .ToDictionary(c => c.PKID, c => c.City_Name);

                    int fromId = bookingDetails.City_From_Id ?? 0;
                    int toId = bookingDetails.City_To_Id ?? 0;

                    string fromCity = cityMap.ContainsKey(fromId) ? cityMap[fromId] : string.Empty;
                    string toCity = cityMap.ContainsKey(toId) ? cityMap[toId] : string.Empty;



                    //Booking allote sucessfull
                    if (obj.Booking_Status_Id == 2)
                    {
                        // Calculate correct paid amount based on payment type
                        decimal paidAmount = 0;
                        decimal remainingAmount = 0;

                        if (bookingDetails.PaymentOption == 1) // Partial payment
                        {
                            paidAmount = bookingDetails.PartialPaymentAmount ?? 0;
                            remainingAmount = bookingDetails.RemainingAmountForDriver ?? 0;
                        }
                        else if (bookingDetails.PaymentOption == 2) // Full online payment
                        {
                            paidAmount = bookingDetails.Fare ?? 0;
                            remainingAmount = 0;
                        }
                        else // Cash payment (PaymentOption == 3)
                        {
                            paidAmount = 0;
                            remainingAmount = bookingDetails.Fare ?? 0;
                        }

                        // For User
                        smsService.SendSMS(new SMSModel
                        {
                            MobileNumber = bookingDetails.Mobile_No1,
                            TemplateType = SMSTemplateType.CustomerBookingConfirmed,
                            MessageVariables = new List<string> { fromCity,
                                toCity,
                                bookingDetails.Booking_Date?.ToString("dd-MM-yyyy"),
                                bookingDetails.Booking_Date?.ToString("hh:mm tt"),
                                bookingDetails.PickUp_Date?.ToString("dd-MM-yyyy"),
                                bookingDetails.PickUp_Time,
                                CarDetails.Car_Number,
                                paidAmount.ToString("F2"), // Correct paid amount
                                remainingAmount.ToString("F2"), // Correct remaining amount
                            }
                        });

                        // For Driver
                        smsService.SendSMS(new SMSModel
                        {
                            MobileNumber = DriverDetails.Drive_Phone_1,
                            TemplateType = SMSTemplateType.DriverBookingConfirmed,
                            MessageVariables = new List<string> { bookingDetails.Name,
                                fromCity,
                                toCity,
                                (bookingDetails.Fare ?? 0).ToString("F2"), // Total fare
                                paidAmount.ToString("F2"), // Use the same calculated paid amount
                                bookingDetails.Booking_Id }
                        });
                    }
                    else if (obj.Booking_Status_Id == 5)
                    {
                        // Booking Cancel - User
                        smsService.SendSMS(new SMSModel
                        {
                            MobileNumber = bookingDetails.Mobile_No1,
                            TemplateType = SMSTemplateType.AdminCancel,
                            MessageVariables = new List<string> { }
                        });

                        // Booking Cancel - Driver
                        smsService.SendSMS(new SMSModel
                        {
                            MobileNumber = DriverDetails.Drive_Phone_1,
                            TemplateType = SMSTemplateType.AdminCancel,
                            MessageVariables = new List<string> { }
                        });
                    }
                    else if (obj.Booking_Status_Id == 6)
                    {
                        // Trip Completed - User
                        smsService.SendSMS(new SMSModel
                        {
                            MobileNumber = bookingDetails.Mobile_No1,
                            TemplateType = SMSTemplateType.RideComplete,
                            MessageVariables = new List<string> { }
                        });

                        // Trip Completed - Driver
                        smsService.SendSMS(new SMSModel
                        {
                            MobileNumber = DriverDetails.Drive_Phone_1,
                            TemplateType = SMSTemplateType.RideComplete,
                            MessageVariables = new List<string> { }
                        });
                    }

                    return Json(1, JsonRequestBehavior.AllowGet);
                }

                return Json("Booking assigned successfully, but SMS is not enabled.", JsonRequestBehavior.AllowGet);

            }
            catch (Exception ex)
            {
                // Log the error to RLT_LOG table for debugging
                try
                {
                    var logEntry = new RLT_LOG
                    {
                        Date = DateTime.Now,
                        Thread = System.Threading.Thread.CurrentThread.ManagedThreadId.ToString(),
                        Level = "ERROR",
                        Logger = "BookingManagementController.AssignCab",
                        Message = $"Error in AssignCab POST method: {ex.Message}",
                        Exception = ex.ToString(),
                        CreatedDate = DateTime.Now,
                        CreatedBy = 1 // Admin user ID
                    };

                    logsRepo.Add(logEntry);
                    logsRepo.Save();
                }
                catch (Exception logEx)
                {
                    // If logging fails, at least write to debug output
                    System.Diagnostics.Debug.WriteLine($"Failed to log AssignCab POST error: {logEx.Message}");
                }

                return Json(ex.Message, JsonRequestBehavior.AllowGet);
            }
        }

        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public string GetInvoiceNo()
        {

            Int64 Count = 0;

            var vlist = (from a in entity.RLT_NextId where a.Type == "InvoiceNo" select a);
            foreach (RLT_NextId v in vlist)
            {
                Count = Convert.ToInt64(v.Count) + 1;
                v.Count = Count;
            }
            entity.SaveChanges();

            string InvoiceNo = "CY" + Count.ToString("D6");
            return InvoiceNo;


        }
        #endregion

        [NonAction]
        public void SendSMS(string mobileNumber, string templateType, string templateName, string smsReplacedwith, string smsReplaceKey)
        {


            var tempalate = entity.RLT_Template_Master.Where(x => x.Mail_SMS_Type == templateType && x.Template_Name == templateName).FirstOrDefault();
            string messageBody = tempalate.Message_Body;

            string textLocalAPI = ConfigurationManager.AppSettings["OTPAPI"].ToString();
            string textLocalKey = ConfigurationManager.AppSettings["OTPAPIKey"].ToString();
            string textLocalSender = ConfigurationManager.AppSettings["Sender"].ToString();
            string replaceTo = ConfigurationManager.AppSettings[smsReplaceKey].ToString();

            String message = HttpUtility.UrlEncode(messageBody.Replace(replaceTo, smsReplacedwith));
            using (var wb = new WebClient())
            {
                byte[] response = wb.UploadValues(textLocalAPI, new NameValueCollection()
                {
                {"apikey" , textLocalKey},
                {"numbers" , mobileNumber},
                {"message" , message},
                {"sender" ,textLocalSender}
                });
                var sendStatus = System.Text.Encoding.UTF8.GetString(response);
            }


        }

        [NonAction]
        public void SendPaymentLinkSMS(M_Booking bookingObj, string paymentUrl, RLT_BOOKING bookingDetails)
        {
            try
            {
                // Get city names for the booking
                var cityMap = entity.RLT_CITY
                    .Where(c => c.PKID == bookingObj.City_From_Id || c.PKID == bookingObj.City_To_Id)
                    .ToDictionary(c => c.PKID, c => c.City_Name);

                string fromCity = (bookingObj.City_From_Id.HasValue && cityMap.ContainsKey(bookingObj.City_From_Id.Value)) ? cityMap[bookingObj.City_From_Id.Value] : "Unknown";
                string toCity = (bookingObj.City_To_Id.HasValue && cityMap.ContainsKey(bookingObj.City_To_Id.Value)) ? cityMap[bookingObj.City_To_Id.Value] : "Unknown";

                // Get car category for cab type
                var carCategory = entity.RLT_CAR_CATEGORY
                    .Where(c => c.PKID == bookingObj.Car_Category_Id)
                    .Select(c => c.Car_Category_Abbr)
                    .FirstOrDefault() ?? "Cab";

                // Calculate minimum booking amount based on payment type
                decimal minBookingAmount = 0;
                if (bookingDetails.PaymentType == "PARTIAL" && bookingDetails.PartialPaymentAmount.HasValue)
                {
                    minBookingAmount = bookingDetails.PartialPaymentAmount.Value;
                }
                else if (bookingDetails.PaymentType == "FULL")
                {
                    minBookingAmount = bookingObj.Fare ?? 0;
                }

                // Format pickup date and time
                string pickupDate = bookingObj.PickUp_Date?.ToString("dd/MM/yyyy") ?? DateTime.Now.ToString("dd/MM/yyyy");
                string pickupTime = bookingObj.PickUp_Time ?? "TBD";

                // Send SMS using the new SMS service with the payment link template
                smsService.SendSMS(new SMSModel
                {
                    MobileNumber = bookingObj.Mobile_No1,
                    TemplateType = SMSTemplateType.PaymentLink,
                    MessageVariables = new List<string> {
                        bookingObj.Name,                    // Customer name
                        fromCity,                           // From city
                        toCity,                             // To city
                        pickupDate,                         // Pickup date
                        pickupTime,                         // Pickup time
                        carCategory,                        // Cab type
                        (bookingObj.Fare ?? 0).ToString("F2"),  // Total fare
                        minBookingAmount.ToString("F2"),    // Min booking amount
                        paymentUrl                          // Payment link
                    }
                });
            }
            catch (Exception ex)
            {
                // Log the error to RLT_LOG table for debugging
                try
                {
                    var logEntry = new RLT_LOG
                    {
                        Date = DateTime.Now,
                        Thread = System.Threading.Thread.CurrentThread.ManagedThreadId.ToString(),
                        Level = "ERROR",
                        Logger = "BookingManagementController.SendPaymentLinkSMS",
                        Message = $"Error sending payment link SMS for booking {bookingObj.Booking_Id}: {ex.Message}",
                        Exception = ex.ToString(),
                        CreatedDate = DateTime.Now,
                        CreatedBy = 1 // Admin user ID
                    };

                    logsRepo.Add(logEntry);
                    logsRepo.Save();
                }
                catch (Exception logEx)
                {
                    // If logging fails, at least write to debug output
                    System.Diagnostics.Debug.WriteLine($"Failed to log SendPaymentLinkSMS error: {logEx.Message}");
                }

                // Log error but don't throw to avoid breaking the booking process
                System.Diagnostics.Debug.WriteLine("Error sending payment link SMS: " + ex.Message);
            }
        }

        [HttpPost]
        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public JsonResult ResendPaymentLinkSMS(string bookingId)
        {
            try
            {
                // Check if SMS is enabled
                string isSMSEnabled = ConfigurationManager.AppSettings["IsSMSEnabled"];
                if (isSMSEnabled != "true")
                {
                    return Json(new { success = false, message = "SMS service is not enabled" });
                }

                // Get booking details from database
                var bookingDetails = entity.RLT_BOOKING.FirstOrDefault(b => b.Booking_Id == bookingId);
                if (bookingDetails == null)
                {
                    return Json(new { success = false, message = "Booking not found" });
                }

                // Check if payment link exists
                if (string.IsNullOrEmpty(bookingDetails.payment_link))
                {
                    return Json(new { success = false, message = "No payment link found for this booking" });
                }

                // Create M_Booking object for SMS sending
                var bookingObj = new M_Booking
                {
                    Booking_Id = bookingDetails.Booking_Id,
                    Name = bookingDetails.Name,
                    Mobile_No1 = bookingDetails.Mobile_No1,
                    City_From_Id = bookingDetails.City_From_Id,
                    City_To_Id = bookingDetails.City_To_Id,
                    PickUp_Date = bookingDetails.PickUp_Date,
                    PickUp_Time = bookingDetails.PickUp_Time,
                    Car_Category_Id = bookingDetails.Car_Category_Id,
                    Fare = bookingDetails.Fare
                };

                // Send SMS using existing method
                SendPaymentLinkSMS(bookingObj, bookingDetails.payment_link, bookingDetails);

                return Json(new { success = true, message = "Payment link SMS sent successfully" });
            }
            catch (Exception ex)
            {
                // Log the error
                try
                {
                    var logEntry = new RLT_LOG
                    {
                        Date = DateTime.Now,
                        Thread = System.Threading.Thread.CurrentThread.ManagedThreadId.ToString(),
                        Level = "ERROR",
                        Logger = "BookingManagementController.ResendPaymentLinkSMS",
                        Message = $"Error resending payment link SMS for booking {bookingId}: {ex.Message}",
                        Exception = ex.ToString(),
                        CreatedDate = DateTime.Now,
                        CreatedBy = 1 // Admin user ID
                    };

                    logsRepo.Add(logEntry);
                    logsRepo.Save();
                }
                catch (Exception logEx)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to log ResendPaymentLinkSMS error: {logEx.Message}");
                }

                return Json(new { success = false, message = "Error sending SMS: " + ex.Message });
            }
        }

    }
}