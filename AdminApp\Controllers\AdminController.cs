﻿using AdminApp.Filters;
using AdminApp.Mapper;
using AdminApp.Services;
using Common.Lib;
using DBLinker.Lib;
using DBLinker.Lib.Model;
using DBLinker.Lib.Repository;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Security;

namespace AdminApp.Controllers
{

    public class AdminController : Controller
    { 
        DBLinker.Lib.RLTDBContext entity;
        IGenericRepository<RLT_ADMIN_USER> adminUserRepo;
        IGenericRepository<RLT_COUNTRY> countryRepo;
        IGenericRepository<RLT_STATE> stateRepo;
        IGenericRepository<RLT_CITY> cityRepo;
        IGenericRepository<RLT_BANK_NAMES> bankNameRepo;
        IGenericRepository<RLT_BOOKING_PAYMENT_DETAILS> bookingPaymentRepo;
        IGenericRepository<RLT_CAR_CATEGORY> carCategortyRepo;
        IGenericRepository<RLT_CAR_CHARGES_FECILITIES_DETAILS> fecilitiesDetailsRepo;
        IGenericRepository<RLT_CAR_COMPANY> carCompanyRepo;
        IGenericRepository<RLT_CAR_DETAILS> carDetailsRepo;
        IGenericRepository<RLT_CAR_DRIVER_DETAILS> carDriverDetailsRepo;
        IGenericRepository<RLT_CAR_FUEL_TYPES> carFuelTypeRepo;
        IGenericRepository<RLT_CAR_MODEL> carModelRepo;
        IGenericRepository<RLT_CAR_OWNER_BANK_DETAILS> carOwnerBankDetailsRepo;
        IGenericRepository<RLT_LOCATION_CODE> locationsRepo;
        IGenericRepository<RLT_LOG> logsRepo;
        IGenericRepository<RLT_MENU_MASTER> menuMasterRepo;
        IGenericRepository<RLT_PAYMENT_METHOD> paymentMethodRepo;
        IGenericRepository<RLT_ROUTES_DETAILS> routeDetailsRepo;
        IGenericRepository<RLT_TRIP_TYPES> tripTypeRepo;
        IGenericRepository<RLT_USER_LOGIN_DETAILS> userLoginDetailsRepo;
        IGenericRepository<RLT_DRIVER_ENQUIRIES> ldriverEnquiryRepo;
        IGenericRepository<RLT_AboutUs> abtUsrRepo;
        IGenericRepository<RLT_FAQ_Details> FAQRepo;
        IGenericRepository<RLT_ContactInformation> contactInfoRepo;
        IGenericRepository<RLT_DRIVER_APPROVE_STATUS> driverAprStatusRepo;
        IGenericRepository<RLT_PrivacyPolicy> priPoRepo;

        IGenericRepository<RLT_TermsConditions> termConRepo;
        IGenericRepository<RLT_UserFeedBack> userFeedbackRepo;
        IGenericRepository<RLT_Services> servicesRepo;
        IGenericRepository<RLT_SUBSCRIBERS> subscribersRepo;
        IGenericRepository<RLT_BOOKING_STATUS> bookingStatusRepo;
        ITwoFactorAuth auth;
        IResizeImage imageResize;
        IGenericRepository<RLT_DOCUMNETS_NAME> docNameRepo;
        IGenericRepository<DBLinker.Lib.TempBooking> tempBookingRepo;
        ObjectMapper map;
        public AdminController(IGenericRepository<RLT_ADMIN_USER> _adminUserRepo,
            IGenericRepository<RLT_COUNTRY> _countryRepo,
        IGenericRepository<RLT_STATE> _stateRepo,
        IGenericRepository<RLT_CITY> _cityRepo,
        IGenericRepository<RLT_BANK_NAMES> _bankNameRepo,
        IGenericRepository<RLT_BOOKING_PAYMENT_DETAILS> _bookingPaymentRepo,
        IGenericRepository<RLT_CAR_CATEGORY> _carCategortyRepo,
        IGenericRepository<RLT_CAR_CHARGES_FECILITIES_DETAILS> _fecilitiesDetailsRepo,
        IGenericRepository<RLT_CAR_COMPANY> _carCompanyRepo,
        IGenericRepository<RLT_CAR_DETAILS> _carDetailsRepo,
        IGenericRepository<RLT_CAR_DRIVER_DETAILS> _carDriverDetailsRepo,
        IGenericRepository<RLT_CAR_FUEL_TYPES> _carFuelTypeRepo,
        IGenericRepository<RLT_CAR_MODEL> _carModelRepo,
        IGenericRepository<RLT_CAR_OWNER_BANK_DETAILS> _carOwnerBankDetailsRepo,
        IGenericRepository<RLT_LOCATION_CODE> _locationsRepo,
        IGenericRepository<RLT_LOG> _logsRepo,
        IGenericRepository<RLT_MENU_MASTER> _menuMasterRepo,
        IGenericRepository<RLT_PAYMENT_METHOD> _paymentMethodRepo,
        IGenericRepository<RLT_ROUTES_DETAILS> _routeDetailsRepo,
        IGenericRepository<RLT_TRIP_TYPES> _tripTypeRepo,


        IGenericRepository<RLT_DRIVER_ENQUIRIES> _ldriverEnquiryRepo,
        IGenericRepository<RLT_AboutUs> _abtUsrRepo,
        IGenericRepository<RLT_FAQ_Details> _FAQRepo,
        IGenericRepository<RLT_ContactInformation> _contactInfoRepo,
         IGenericRepository<RLT_DRIVER_APPROVE_STATUS> _driverAprStatusRepo,
        IGenericRepository<RLT_PrivacyPolicy> _priPoRepo,

         IGenericRepository<RLT_TermsConditions> _termConRepo,
          IGenericRepository<RLT_UserFeedBack> _userFeedbackRepo,
           IGenericRepository<RLT_Services> _servicesRepo,
           IGenericRepository<RLT_SUBSCRIBERS> _subscribersRepo,
             IGenericRepository<RLT_DOCUMNETS_NAME> _docNameRepo,
             ObjectMapper _map,
        IGenericRepository<RLT_USER_LOGIN_DETAILS> _userLoginDetailsRepo, IGenericRepository<RLT_BOOKING_STATUS> _bookingStatusRepo, IGenericRepository<DBLinker.Lib.TempBooking> _tempBookingRepo, ITwoFactorAuth _auth, IResizeImage _imageResize)
        {
            adminUserRepo = _adminUserRepo;
            countryRepo = _countryRepo;
            stateRepo = _stateRepo;
            cityRepo = _cityRepo;
            bankNameRepo = _bankNameRepo;
            bookingPaymentRepo = _bookingPaymentRepo;
            carCategortyRepo = _carCategortyRepo;
            fecilitiesDetailsRepo = _fecilitiesDetailsRepo;
            carCompanyRepo = _carCompanyRepo;
            carDetailsRepo = _carDetailsRepo;
            carDriverDetailsRepo = _carDriverDetailsRepo;
            carFuelTypeRepo = _carFuelTypeRepo;
            carModelRepo = _carModelRepo;
            carOwnerBankDetailsRepo = _carOwnerBankDetailsRepo;
            locationsRepo = _locationsRepo;
            logsRepo = _logsRepo;
            menuMasterRepo = _menuMasterRepo;
            paymentMethodRepo = _paymentMethodRepo;
            routeDetailsRepo = _routeDetailsRepo;
            tripTypeRepo = _tripTypeRepo;
            userLoginDetailsRepo = _userLoginDetailsRepo;


            ldriverEnquiryRepo = _ldriverEnquiryRepo;
            abtUsrRepo = _abtUsrRepo;
            FAQRepo = _FAQRepo;
            contactInfoRepo = _contactInfoRepo;
            driverAprStatusRepo = _driverAprStatusRepo;
            priPoRepo = _priPoRepo;
            termConRepo = _termConRepo;
            userFeedbackRepo = _userFeedbackRepo;
            servicesRepo = _servicesRepo;
            imageResize = _imageResize;
            auth = _auth;
            subscribersRepo = _subscribersRepo;
            bookingStatusRepo = _bookingStatusRepo;
            docNameRepo = _docNameRepo;
            tempBookingRepo = _tempBookingRepo;
            map = _map;
            entity = new DBLinker.Lib.RLTDBContext();
        }
        // GET: Admin
        public ActionResult Dashboard()
        {

            var sql = @"
        SELECT
            (SELECT COUNT(*) FROM RLT_Vendor_Details WHERE Is_Active = 1) AS TotalVendors,
            (SELECT COUNT(*) FROM RLT_CAR_DETAILS WHERE Is_Active = 1) AS TotalCars,
            (SELECT COUNT(*) FROM RLT_CAR_DRIVER_DETAILS WHERE Is_Active = 1) AS TotalDrivers,
            (SELECT COUNT(*) FROM RLT_BOOKING) AS TotalBookings";
            var counts = entity.Database.SqlQuery<DashboardCountsDto>(sql).FirstOrDefault();
            ViewBag.DashboardCounts = counts;


            var BookingList = (from a in entity.RLT_BOOKING
                               join d in entity.RLT_TRIP_TYPES on a.Trip_Type_Id equals d.PKID
                               join b in entity.RLT_CITY on a.City_From_Id equals b.PKID
                               join c in entity.RLT_CITY on a.City_To_Id equals c.PKID
                               join f in entity.RLT_BOOKING_STATUS on a.Booking_Status_Id equals f.PKID
                               orderby a.Booking_Date descending
                               select new M_Bookings
                               {
                                   PKID = a.PKID,
                                   Booking_Id = a.Booking_Id,
                                   City_From = b.City_Name,
                                   City_To = c.City_Name,
                                   PickUp_Date = a.PickUp_Date,
                                   Booking_Status = f.BOOKING_STATUS,
                                   Booking_Date = a.Booking_Date,
                                   Trip_Type = d.Trip_Type,
                                   Updated_Date = a.Updated_Date,
                                   Booking_Status_Id = f.PKID

                               }).ToList().Where(x => x.PickUp_Date < DateTime.Today.AddDays(15));

            ViewBag.BookingList = BookingList.Where(x => x.Booking_Status_Id != 5 && x.Booking_Status_Id != 6); ;
            ViewBag.BookingListUpComing = BookingList.Where(x => x.Booking_Status_Id == 1 && x.Booking_Status_Id == 2);
            ViewBag.BookingListCompleted = BookingList.Where(x => x.Booking_Status_Id == 6);

            var DriverList = (from a in entity.RLT_DRIVER_ENQUIRIES
                              join f in entity.RLT_DRIVER_APPROVE_STATUS on a.Driver_Approving_Status equals f.PKID
                              orderby a.RequestDate descending
                              select new M_Driver_Enquiries
                              {
                                  PKID = a.PKID,
                                  Driver_Name = a.Driver_Name,
                                  RequestDate = a.RequestDate,
                                  Driver_Mail = a.Driver_Mail,
                                  Driver_Phone_1 = a.Driver_Phone_1,
                                  IsWhatsAppNumber = a.IsWhatsAppNumber

                              }).ToList();
            ViewBag.DriverList = DriverList;

            var ContactEnquiryList = (from a in entity.RLT_CONTACT_ENQUIRIES
                                      orderby a.RequestDate descending
                                      select a).ToList();

            ViewBag.ContactEnquiryList = ContactEnquiryList;



            return View();
        }

        #region Country Manager

        public ActionResult CountryManager()
        {
            var countryModel = new RLT_COUNTRY();
            var countryGrid = countryRepo.GetAll().ToList();
            ViewBag.countryGrid = countryGrid;
            return View(countryModel);
        }

        [HttpPost]
        // [ValidateAntiForgeryToken]
        public JsonResult CountryManager(RLT_COUNTRY country)
        {

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                countryRepo.Edit(country);
                countryRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                countryRepo.Delete(country);
                countryRepo.Save();
                Id = 3;
            }
            else
            {

                countryRepo.Add(country);
                countryRepo.Save();
                Id = country.PKID;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }

        #endregion

        #region State Manager
        public ActionResult StateManager()
        {
            var countryList = countryRepo.GetAll().ToList();
            ViewBag.countryList = countryList;
            var data = entity.RLT_STATE.ToList();
            ViewBag.stateGrid = data;

            var stateModel = new RLT_STATE();
            return View(stateModel);
        }

        [HttpPost]
        // [ValidateAntiForgeryToken]
        public JsonResult StateManager(RLT_STATE state)
        {

            int Id = 0;

            if (Request.Form["hdnOperation"].Equals("U"))
            {


                stateRepo.Edit(state);
                stateRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {

                state.Is_Active = Request.Form["hdnOperation"].Equals("D");
                stateRepo.Edit(state);
                stateRepo.Save();
                Id = 3;
            }
            else
            {

                try
                {

                    stateRepo.Add(state);
                    stateRepo.Save();
                    Id = state.PKID;

                }
                catch (Exception ex)
                {
                    return Json(ex.Message, JsonRequestBehavior.AllowGet);
                }

            }

            return Json(Id, JsonRequestBehavior.AllowGet);

        }

        #endregion


        #region City Manager

        public ActionResult CityManager()
        {
            var cityGridList = entity.RLT_CITY.ToList();
            ViewBag.cityGridList = cityGridList;

            var StateList = stateRepo.GetAll().Where(x => x.Is_Active == true).ToList();
            ViewBag.StateList = StateList;

            var CountryList = countryRepo.GetAll().Where(x => x.Is_Active == true).ToList();
            ViewBag.CountryList = CountryList;

            var model = new M_City();
            return View(model);
        }

        [HttpPost]
        //[ValidateAntiForgeryToken]
        public JsonResult CityManager(M_City city)
        {
            RLT_CITY obj1 = new RLT_CITY();
            obj1.PKID = city.PKID;
            obj1.State_PKID = city.State_PKID;
            obj1.City_Name = city.City_Name;
            obj1.latitude = city.latitude;
            obj1.longitude = city.longitude;
            obj1.eLoc = city.eLoc;
            obj1.score = city.score;
            obj1.Is_Active = city.Is_Active;

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                obj1.Is_Active = city.Is_Active;
                cityRepo.Edit(obj1);
                cityRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                obj1.Is_Active = city.Is_Active;
                cityRepo.Edit(obj1);
                cityRepo.Save();
                Id = 3;
            }
            else
            {

                obj1.Is_Active = true;
                cityRepo.Add(obj1);
                cityRepo.Save();
                Id = 1;

            }

            return Json(Id, JsonRequestBehavior.AllowGet);
        }
        #endregion


        #region Subscriber Manager
        public ActionResult SubscriberManager()
        {

            var newsGridList = subscribersRepo.GetAll().ToList();
            ViewBag.newsGridList = newsGridList;

            var model = new RLT_SUBSCRIBERS();
            return View(model);
        }

        [HttpPost]
        //[ValidateAntiForgeryToken]
        public JsonResult SubscriberManager(RLT_SUBSCRIBERS newLetter)
        {

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                subscribersRepo.Edit(newLetter);
                subscribersRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                subscribersRepo.Delete(newLetter);
                subscribersRepo.Save();
                Id = 3;
            }
            else
            {
                subscribersRepo.Add(newLetter);
                subscribersRepo.Save();
                Id = newLetter.ID;

            }

            return Json(Id, JsonRequestBehavior.AllowGet);
        }
        #endregion



        #region User FeedBAck Manager
        public ActionResult UserFeedBack()
        {

            var FeddbackList = userFeedbackRepo.GetAll().ToList();
            ViewBag.FeddbackList = FeddbackList;

            var model = new RLT_UserFeedBack();
            return View(model);
        }

        [HttpPost]
        //[ValidateAntiForgeryToken]
        public ActionResult UserFeedBack(RLT_UserFeedBack feedback)
        {

            int Id = 0;
            string filename = feedback.Photo;
            if (Request.Form["hdnOperation"].Equals("U"))
            {

                for (int i = 0; i < Request.Files.Count; i++)
                {
                    var file = Request.Files[i];
                    if (file != null && file.ContentLength > 0)
                    {
                        if (file != null && file.ContentLength > 0)
                        {
                            try
                            {
                                feedback.Photo = file.FileName;
                                userFeedbackRepo.Edit(feedback);
                                userFeedbackRepo.Save();
                                Id = feedback.PKID;
                                if (Id > 0)
                                {
                                    string path = Path.Combine(Server.MapPath("~/UserFeedBack"), Path.GetFileName(file.FileName));
                                    file.SaveAs(path);
                                }
                            }
                            catch (Exception ex)
                            {
                                return Json(ex.Message, JsonRequestBehavior.AllowGet);
                            }
                        }
                        else
                        {
                            return Json("Please Attach Image", JsonRequestBehavior.AllowGet);
                        }
                    }
                    else if (!string.IsNullOrEmpty(feedback.Photo))
                    {
                        feedback.Photo = filename;
                        userFeedbackRepo.Edit(feedback);
                        userFeedbackRepo.Save();
                        Id = feedback.PKID;
                    }
                }
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {


                feedback.IsActive = Request.Form["hdnOperation"].Equals("D");
                userFeedbackRepo.Add(feedback);
                userFeedbackRepo.Save();


            }
            else
            {
                for (int i = 0; i < Request.Files.Count; i++)
                {
                    var file = Request.Files[i];
                    if (file != null && file.ContentLength > 0)
                    {
                        try
                        {
                            feedback.Photo = file.FileName;
                            userFeedbackRepo.Add(feedback);
                            userFeedbackRepo.Save();
                            string path = Path.Combine(Server.MapPath("~/UserFeedBack"), Path.GetFileName(file.FileName));
                            file.SaveAs(path);

                        }
                        catch (Exception ex)
                        {
                            return Json(ex.Message, JsonRequestBehavior.AllowGet);
                        }
                    }
                    else
                    {
                        return Json("Please Attach Image", JsonRequestBehavior.AllowGet);
                    }
                }
            }

            var FeddbackList = userFeedbackRepo.GetAll().ToList();
            ViewBag.FeddbackList = FeddbackList;

            return View();

        }
        #endregion



        #region PrivacyAndPolicy Manager
        public ActionResult PrivacyAndPolicy()
        {
            var PrivacyPolicies = priPoRepo.GetAll().ToList();
            ViewBag.PrivacyPolicies = PrivacyPolicies;

            return View();
        }

        [HttpPost, ValidateInput(false)]
        // [ValidateAntiForgeryToken]
        public JsonResult PrivacyAndPolicy(RLT_PrivacyPolicy policy)
        {

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                priPoRepo.Edit(policy);
                priPoRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                priPoRepo.Delete(policy);
                priPoRepo.Save();
                Id = 3;
            }
            else
            {
                priPoRepo.Add(policy);
                priPoRepo.Save();
                Id = policy.PKID;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }

        #endregion

        #region TermsAndConditions Manager
        public ActionResult TermsAndConditions()
        {
            var TermsConditions = termConRepo.GetAll().ToList();
            ViewBag.TermsConditions = TermsConditions;


            var model = new RLT_TermsConditions();
            return View(model);
        }

        [HttpPost, ValidateInput(false)]
        // [ValidateAntiForgeryToken]
        public JsonResult TermsAndConditions(RLT_TermsConditions terms)
        {

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                termConRepo.Edit(terms);
                termConRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                termConRepo.Delete(terms);
                termConRepo.Save();
                Id = 3;
            }
            else
            {
                termConRepo.Add(terms);
                termConRepo.Save();
                Id = terms.PKID;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }


        #endregion


        #region TwoFactorAuth Manager
        public ActionResult TwoFactorAuth()
        {
            Common.Lib.Model.RLT_ADMIN_USER adminUser = new Common.Lib.Model.RLT_ADMIN_USER();
            string UserName = Convert.ToString(((CustomPrincipal)(User)).UserName);
            var userAuth = adminUserRepo.GetAll().Where(x => (x.UserName == UserName && x.IsActive == true)).FirstOrDefault();
            if (userAuth != null)
            {

                adminUser.UserFirstName = userAuth.UserFirstName;
                adminUser.UserLastName = userAuth.UserLastName;
                adminUser.UserName = userAuth.UserName;
                adminUser.UserPWD = userAuth.UserPWD;
                adminUser.Is2TfaAuthentication = userAuth.Is2TfaAuthentication;
                adminUser.UserEmailID = userAuth.UserEmailID;
            }

            return View(adminUser);
        }

        [HttpGet]

        public ActionResult ActiveInactiveTwoFactorAuth(string status)
        {
            string UserName = Convert.ToString(((CustomPrincipal)(User)).UserName);
            if (Convert.ToBoolean(status))
            {
                Common.Lib.Model.TwoFactorModel model = new Common.Lib.Model.TwoFactorModel();

                var userAuth = adminUserRepo.GetAll().Where(x => (x.UserName == UserName && x.IsActive == true)).FirstOrDefault();
                if (userAuth != null)
                {
                    model = auth.GenerateQRCode(userAuth.UserName, userAuth.UserPWD, userAuth.UserEmailID);
                    TempData["model"] = model;
                    Session["UniqueKey"] = model.UserUniqueKey;
                    return RedirectToAction("GenerateQRCODE");

                }
            }
            else
            {
                RLT_ADMIN_USER objUser = new RLT_ADMIN_USER();
                try
                {
                    var userData = adminUserRepo.GetAll().Where(x => (x.UserName == UserName && x.IsActive == true)).FirstOrDefault();

                    objUser = (RLT_ADMIN_USER)userData;
                    objUser.Is2TfaAuthentication = false;

                    adminUserRepo.Edit(objUser);
                    adminUserRepo.Save();
                }

                finally
                {
                    objUser = null;
                }
            }
            return RedirectToAction("TwoFactorAuth");

        }


        public ActionResult GenerateQRCODE(Common.Lib.Model.TwoFactorModel model)
        {
            if (TempData["model"] != null)
            {
                return View(TempData["model"]);
            }
            else
            {
                return RedirectToAction("TwoFactorAuth");
            }
        }
        [HttpPost]
        public JsonResult VerifyGeneratedQRCODE(string PassCode)
        {
            RLT_ADMIN_USER adminUser = new RLT_ADMIN_USER();
            bool isValidate = false;
            string UserName = Convert.ToString(((CustomPrincipal)(User)).UserName);

            string UserUniqueKey = Convert.ToString(Session["UniqueKey"]);
            if (!string.IsNullOrEmpty(PassCode) && !string.IsNullOrEmpty(UserUniqueKey))
            {
                isValidate = auth.Verify2FA(PassCode, UserUniqueKey);
            }
            if (isValidate)
            {
                Session["UniqueKey"] = "";

                try
                {
                    var userData = adminUserRepo.GetAll().Where(x => (x.UserName == UserName && x.IsActive == true)).FirstOrDefault();

                    adminUser = (RLT_ADMIN_USER)userData;
                    adminUser.Is2TfaAuthentication = true;

                    adminUserRepo.Edit(adminUser);
                    adminUserRepo.Save();

                }

                finally
                {
                    adminUser = null;
                }
            }
            return Json(isValidate, JsonRequestBehavior.AllowGet);
        }

        #endregion

        #region Logout
        public ActionResult LogOut()
        {
            FormsAuthentication.SignOut();
            Session["MenuList"] = null;
            return RedirectToAction("Index", "Login", null);
        }
        #endregion


        #region About us Manager

        public ActionResult AboutUsManager()
        {
            var aboutUsGrid = abtUsrRepo.GetAll().ToList();
            ViewBag.aboutUsGrid = aboutUsGrid;

            var model = new RLT_AboutUs();
            return View(model);
        }

        [HttpPost, ValidateInput(false)]
        // [ValidateAntiForgeryToken]
        public JsonResult AboutUsManager(RLT_AboutUs aboutus)
        {

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                abtUsrRepo.Edit(aboutus);
                abtUsrRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                abtUsrRepo.Delete(aboutus);
                abtUsrRepo.Save();
                Id = 3;
            }
            else
            {
                abtUsrRepo.Add(aboutus);
                abtUsrRepo.Save();
                Id = aboutus.PKID;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }

        #endregion

        #region FAQ

        public ActionResult FAQManager()
        {
            var FAQGrid = FAQRepo.GetAll().ToList();
            ViewBag.FAQGrid = FAQGrid;

            var model = new RLT_FAQ_Details();
            return View(model);
        }

        [HttpPost, ValidateInput(false)]
        // [ValidateAntiForgeryToken]
        public JsonResult FAQManager(RLT_FAQ_Details FAQ)
        {

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                FAQRepo.Edit(FAQ);
                FAQRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                FAQRepo.Delete(FAQ);
                FAQRepo.Save();
                Id = 3;
            }
            else
            {
                FAQRepo.Add(FAQ);
                FAQRepo.Save();
                Id = FAQ.PKID;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }

        #endregion


        #region Service Manager

        public ActionResult ServiceManager()
        {
            var serviceGrid = servicesRepo.GetAll().ToList();
            ViewBag.serviceGrid = serviceGrid;

            var model = new RLT_Services();
            return View(model);
        }

        [HttpPost, ValidateInput(false)]
        // [ValidateAntiForgeryToken]
        public JsonResult ServiceManager(RLT_Services service)
        {

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                servicesRepo.Edit(service);
                servicesRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                servicesRepo.Delete(service);
                servicesRepo.Save();
                Id = 3;
            }
            else
            {
                servicesRepo.Add(service);
                servicesRepo.Save();
                Id = service.PKID;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }

        #endregion



        #region FuelManager
        public ActionResult FuelType()
        {
            var FuelTypeRepo = carFuelTypeRepo.GetAll().ToList();
            ViewBag.FuelTypeRepo = FuelTypeRepo;

            var model = new RLT_CAR_FUEL_TYPES();
            return View(model);
        }


        [HttpPost, ValidateInput(false)]
        // [ValidateAntiForgeryToken]
        public JsonResult FuelType(RLT_CAR_FUEL_TYPES fuelType)
        {

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                carFuelTypeRepo.Edit(fuelType);
                carFuelTypeRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                carFuelTypeRepo.Delete(fuelType);
                carFuelTypeRepo.Save();
                Id = 3;
            }
            else
            {
                carFuelTypeRepo.Add(fuelType);
                carFuelTypeRepo.Save();
                Id = fuelType.PKID;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }
        #endregion


        #region Car Category
        public ActionResult CarCategory()
        {
            var CarCategory = carCategortyRepo.GetAll().ToList();
            ViewBag.carCategortyRepo = CarCategory;

            var model = new RLT_CAR_CATEGORY();
            return View(model);
        }


        [HttpPost, ValidateInput(false)]
        // [ValidateAntiForgeryToken]
        public JsonResult CarCategory(RLT_CAR_CATEGORY carCategory, HttpPostedFileBase CarCategoryImage)
        {
            if (CarCategoryImage != null)
            {
                string filename = Guid.NewGuid() + "_" + CarCategoryImage.FileName;
                string Savepath = Server.MapPath("~/Docs/CarCategory/" + filename);

                // Ensure directory exists
                string directory = Path.GetDirectoryName(Savepath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                CarCategoryImage.SaveAs(Savepath);
                carCategory.Car_Categroy_Image = filename;
            }

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                carCategortyRepo.Edit(carCategory);
                carCategortyRepo.Save();
                Id = 2;
            }
            // to enable or disable CarCategory
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                var getCategory = carCategortyRepo.FindBy(x => x.PKID == carCategory.PKID).FirstOrDefault();
                getCategory.Is_Active = getCategory.Is_Active != null ? (getCategory.Is_Active == true ? false : true) : false;
                carCategortyRepo.Edit(getCategory);
                carCategortyRepo.Save();
                Id = 3;
            }
            else
            {
                carCategory.Is_Active = true;
                carCategortyRepo.Add(carCategory);
                carCategortyRepo.Save();
                Id = carCategory.PKID;
            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }

        [HttpGet]
        public JsonResult CarCategoryEdit(int pkid)
        {
            bool status = false;
            var carCategory = entity.RLT_CAR_CATEGORY.Where(x => x.PKID == pkid).Select(x => new
            {
                x.Car_Category_Name,
                x.Car_Category_Abbr,
                x.Per_KM_fare,
                x.Capacity,
                x.Features,
                x.Is_Active,
                x.Car_Categroy_Image,
                x.PKID
            }).FirstOrDefault();
            //carCategory.Car_Categroy_Image
            if (carCategory != null) return Json(carCategory, JsonRequestBehavior.AllowGet);
            return Json(status, JsonRequestBehavior.AllowGet);
        }

        #endregion


        #region Car Model
        public ActionResult CARModel()
        {

            var carCompany = carCompanyRepo.GetAll().ToList();
            ViewBag.carCompanyRepo = carCompany;

            var CarCategory = carCategortyRepo.GetAll().ToList();
            ViewBag.carCategortyRepo = CarCategory;

            var CarSegment = entity.RLT_CAR_SEGMENT.ToList(); ;
            ViewBag.CarSegment = CarSegment;

            //var CarModel = carModelRepo.GetAll().ToList();
            //ViewBag.CarModel = CarModel;

            var FuelTypeRepo = carFuelTypeRepo.GetAll().ToList();
            ViewBag.FuelTypeRepo = FuelTypeRepo;


            var CarModelList = (from a in entity.RLT_CAR_MODEL
                                join b in entity.RLT_CAR_COMPANY on a.Car_Company_PKID equals b.PKID
                                join c in entity.RLT_CAR_CATEGORY on a.Car_Category_PKID equals c.PKID
                                //join d in entity.RLT_CAR_SEGMENT on a.Car_Segment_PKID equals d.PKID
                                join e in entity.RLT_CAR_FUEL_TYPES on a.Car_Fuel_Type_Status equals e.PKID

                                join p in entity.RLT_CAR_SEGMENT on a.Car_Segment_PKID equals p.PKID into ps
                                from d in ps.DefaultIfEmpty()

                                select new M_Car_Model
                                {
                                    PKID = a.PKID,
                                    Car_Model_Name = a.Car_Model_Name,
                                    Car_Company_PKID = a.Car_Company_PKID,
                                    Car_Category_PKID = a.Car_Category_PKID,
                                    Car_Segment_PKID = a.Car_Segment_PKID,
                                    Car_Fuel_Type_Status = a.Car_Fuel_Type_Status,
                                    Is_Active = a.Is_Active,
                                    Company_Name = b.Company_Name,
                                    Car_Category_Name = c.Car_Category_Abbr,
                                    Car_Segment_Name = d.Car_Segment,
                                    CAR_FUEL_TYPE = e.CAR_FUEL_TYPE,

                                }).ToList();
            ViewBag.CarModel = CarModelList;


            var model = new M_Car_Model();
            return View(model);
        }


        [HttpPost, ValidateInput(false)]
        // [ValidateAntiForgeryToken]
        public JsonResult CARModel(M_Car_Model Obj1)
        {
            RLT_CAR_MODEL carModel = new RLT_CAR_MODEL();
            carModel.PKID = Obj1.PKID;
            carModel.Car_Company_PKID = Obj1.Car_Company_PKID;
            carModel.Car_Category_PKID = Obj1.Car_Category_PKID;
            carModel.Car_Fuel_Type_Status = Obj1.Car_Fuel_Type_Status;
            carModel.Car_Model_Name = Obj1.Car_Model_Name;
            carModel.Car_Segment_PKID = Obj1.Car_Segment_PKID;

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                carModel.Is_Active = Obj1.Is_Active;
                carModelRepo.Edit(carModel);
                carModelRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                carModelRepo.Delete(carModel);
                carModelRepo.Save();
                Id = 3;
            }
            else
            {
                carModel.Is_Active = true;
                carModelRepo.Add(carModel);
                carModelRepo.Save();
                Id = 1;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }
        #endregion

        #region Car Comapny
        public ActionResult CARCompany()
        {
            var carCompany = carCompanyRepo.GetAll().ToList();
            ViewBag.carCompanyRepo = carCompany;

            var model = new M_Company();
            return View(model);
        }


        [HttpPost, ValidateInput(false)]
        // [ValidateAntiForgeryToken]
        public JsonResult CARCompany(M_Company Obj1)
        {
            RLT_CAR_COMPANY carCompany = new RLT_CAR_COMPANY();
            carCompany.PKID = Obj1.PKID;
            carCompany.Company_Name = Obj1.Company_Name;

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                carCompany.Is_Active = Obj1.Is_Active;
                carCompanyRepo.Edit(carCompany);
                carCompanyRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                carCompanyRepo.Delete(carCompany);
                carCompanyRepo.Save();
                Id = 3;
            }
            else
            {
                carCompany.Is_Active = true;
                carCompanyRepo.Add(carCompany);
                carCompanyRepo.Save();
                Id = 1;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }
        #endregion

        #region BanK Name
        public ActionResult BankName()
        {
            var BankName = bankNameRepo.GetAll().ToList();
            ViewBag.BankName = bankNameRepo;
            var model = new RLT_BANK_NAMES();
            return View(model);
        }


        [HttpPost, ValidateInput(false)]
        // [ValidateAntiForgeryToken]
        public JsonResult BankName(RLT_BANK_NAMES bankName)
        {

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                bankNameRepo.Edit(bankName);
                bankNameRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                bankNameRepo.Delete(bankName);
                bankNameRepo.Save();
                Id = 3;
            }
            else
            {
                bankNameRepo.Add(bankName);
                bankNameRepo.Save();
                Id = bankName.PKID;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }
        #endregion



        #region CAR Details
        public ActionResult CARDETAILS()
        {
            var carDetails = carDetailsRepo.GetAll().ToList();
            ViewBag.carDetailsRepo = carDetails;
            var model = new RLT_CAR_DETAILS();
            return View(model);
        }


        [HttpPost, ValidateInput(false)]
        // [ValidateAntiForgeryToken]
        public JsonResult CARDETAILS(RLT_CAR_DETAILS carDetails)
        {

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                carDetailsRepo.Edit(carDetails);
                carDetailsRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                carDetailsRepo.Delete(carDetails);
                carDetailsRepo.Save();
                Id = 3;
            }
            else
            {
                carDetailsRepo.Add(carDetails);
                carDetailsRepo.Save();
                Id = carDetails.PKID;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }
        #endregion


        #region CAR Driver Details
        public ActionResult CARDRIVERDETAILS()
        {
            var carDriverDetails = carDriverDetailsRepo.GetAll().ToList();
            ViewBag.carDriverDetails = carDriverDetails;
            var model = new RLT_CAR_DRIVER_DETAILS();
            return View(model);
        }


        [HttpPost, ValidateInput(false)]
        // [ValidateAntiForgeryToken]
        public JsonResult CARDRIVERDETAILS(RLT_CAR_DRIVER_DETAILS carDriverDetails)
        {

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                carDriverDetailsRepo.Edit(carDriverDetails);
                carDriverDetailsRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                carDriverDetailsRepo.Delete(carDriverDetails);
                carDriverDetailsRepo.Save();
                Id = 3;
            }
            else
            {
                carDriverDetailsRepo.Add(carDriverDetails);
                carDriverDetailsRepo.Save();
                Id = carDriverDetails.PKID;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }
        #endregion



        #region CAR Owner Bank Details
        public ActionResult CarOwnerBankDetails()
        {
            var carOwnerBankDetails = carOwnerBankDetailsRepo.GetAll().ToList();
            ViewBag.carOwnerBankDetails = carOwnerBankDetails;
            var model = new RLT_CAR_OWNER_BANK_DETAILS();
            return View(model);
        }


        [HttpPost, ValidateInput(false)]
        // [ValidateAntiForgeryToken]
        public JsonResult CarOwnerBankDetails(RLT_CAR_OWNER_BANK_DETAILS carOwnerBankDetails)
        {

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                carOwnerBankDetailsRepo.Edit(carOwnerBankDetails);
                carOwnerBankDetailsRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                carOwnerBankDetailsRepo.Delete(carOwnerBankDetails);
                carOwnerBankDetailsRepo.Save();
                Id = 3;
            }
            else
            {
                carOwnerBankDetailsRepo.Add(carOwnerBankDetails);
                carOwnerBankDetailsRepo.Save();
                Id = carOwnerBankDetails.PKID;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }
        #endregion


        #region Location Code
        public ActionResult LOCATIONCODE()
        {

            var cityGridList = entity.RLT_CITY.ToList();
            ViewBag.cityGridList = cityGridList;

            var LOCATIONCODE = locationsRepo.GetAll().ToList();
            ViewBag.LOCATIONCODE = LOCATIONCODE;

            var model = new RLT_LOCATION_CODE();
            return View(model);
        }


        [HttpPost, ValidateInput(false)]
        // [ValidateAntiForgeryToken]
        public JsonResult LOCATIONCODE(RLT_LOCATION_CODE locationCode)
        {

            long Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                locationsRepo.Edit(locationCode);
                locationsRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                locationsRepo.Delete(locationCode);
                locationsRepo.Save();
                Id = 3;
            }
            else
            {
                locationsRepo.Add(locationCode);
                locationsRepo.Save();
                Id = locationCode.PKID;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }
        #endregion

        #region  Payment Method
        public ActionResult PAYMENTMETHOD()
        {
            var paymentMethod = paymentMethodRepo.GetAll().ToList();
            ViewBag.paymentMethod = paymentMethod;
            var model = new RLT_PAYMENT_METHOD();
            return View(model);
        }




        [HttpPost, ValidateInput(false)]

        // [ValidateAntiForgeryToken]
        public JsonResult PAYMENTMETHOD(RLT_PAYMENT_METHOD paymentMethod)
        {

            long Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                paymentMethodRepo.Edit(paymentMethod);
                paymentMethodRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                paymentMethodRepo.Delete(paymentMethod);
                paymentMethodRepo.Save();
                Id = 3;
            }
            else
            {
                paymentMethodRepo.Add(paymentMethod);
                paymentMethodRepo.Save();
                Id = paymentMethod.PKID;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }
        #endregion


        #region  Trip Type
        public ActionResult TripType()
        {
            var tripType = tripTypeRepo.GetAll().ToList();
            ViewBag.tripType = tripType;
            var model = new M_Trip_Types();
            return View(model);
        }


        [HttpPost, ValidateInput(false)]

        // [ValidateAntiForgeryToken]
        public JsonResult TripType(M_Trip_Types Obj1)
        {
            RLT_TRIP_TYPES tripType = new RLT_TRIP_TYPES();
            tripType.PKID = Obj1.PKID;
            tripType.Trip_Type = Obj1.Trip_Type;
            long Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                tripType.Is_Active = Obj1.Is_Active;
                tripTypeRepo.Edit(tripType);
                tripTypeRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                tripTypeRepo.Delete(tripType);
                tripTypeRepo.Save();
                Id = 3;
            }
            else
            {
                tripType.Is_Active = true;
                tripTypeRepo.Add(tripType);
                tripTypeRepo.Save();
                Id = 1;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }
        #endregion


        #region  Route Details
        public ActionResult RouteDetails()
        {
            var RouteDetails = routeDetailsRepo.GetAll().ToList();
            ViewBag.RouteDetails = RouteDetails;
            var model = new RLT_ROUTES_DETAILS();
            return View(model);
        }


        [HttpPost, ValidateInput(false)]

        // [ValidateAntiForgeryToken]
        public JsonResult RouteDetails(RLT_ROUTES_DETAILS routeDetails)
        {

            long Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                routeDetailsRepo.Edit(routeDetails);
                routeDetailsRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                routeDetailsRepo.Delete(routeDetails);
                routeDetailsRepo.Save();
                Id = 3;
            }
            else
            {
                routeDetailsRepo.Add(routeDetails);
                routeDetailsRepo.Save();
                Id = routeDetails.PKID;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }
        #endregion


        #region  Car Charges Facilities Details
        public ActionResult CarChargesFacilitiesDetails()
        {
            var CarChargesFacilitiesDetails = fecilitiesDetailsRepo.GetAll().ToList();
            ViewBag.CarChargesFacilitiesDetails = CarChargesFacilitiesDetails;
            var model = new RLT_CAR_CHARGES_FECILITIES_DETAILS();
            return View(model);
        }


        [HttpPost, ValidateInput(false)]

        // [ValidateAntiForgeryToken]
        public JsonResult CarChargesFacilitiesDetails(RLT_CAR_CHARGES_FECILITIES_DETAILS carChargesFecility)
        {

            long Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                fecilitiesDetailsRepo.Edit(carChargesFecility);
                fecilitiesDetailsRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                fecilitiesDetailsRepo.Delete(carChargesFecility);
                fecilitiesDetailsRepo.Save();
                Id = 3;
            }
            else
            {
                fecilitiesDetailsRepo.Add(carChargesFecility);
                fecilitiesDetailsRepo.Save();
                Id = carChargesFecility.PKID;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }
        #endregion


        #region Booking Status
        public ActionResult BookingStatus()
        {
            var BookingStatus = bookingStatusRepo.GetAll().ToList();
            ViewBag.BookingStatus = BookingStatus;
            var model = new RLT_BOOKING_STATUS();
            return View(model);
        }


        [HttpPost, ValidateInput(false)]
        // [ValidateAntiForgeryToken]
        public JsonResult BookingStatus(RLT_BOOKING_STATUS Obj1)
        {
            RLT_BOOKING_STATUS BookingStatus = new RLT_BOOKING_STATUS();
            BookingStatus.PKID = Obj1.PKID;
            BookingStatus.BOOKING_STATUS = Obj1.BOOKING_STATUS;

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                BookingStatus.Is_Active = Obj1.Is_Active;
                bookingStatusRepo.Edit(BookingStatus);
                bookingStatusRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                bookingStatusRepo.Delete(BookingStatus);
                bookingStatusRepo.Save();
                Id = 3;
            }
            else
            {
                BookingStatus.Is_Active = true;
                bookingStatusRepo.Add(BookingStatus);
                bookingStatusRepo.Save();
                Id = 1;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }
        #endregion

        #region Drive CarApprove Status
        public ActionResult DriveApproveStatus()
        {
            var DriverAprStatus = driverAprStatusRepo.GetAll().ToList();
            ViewBag.DriverAprStatus = DriverAprStatus;
            var model = new RLT_DRIVER_APPROVE_STATUS();
            return View(model);
        }


        [HttpPost, ValidateInput(false)]
        // [ValidateAntiForgeryToken]
        public JsonResult DriveApproveStatus(RLT_DRIVER_APPROVE_STATUS DriverAprStatus)
        {

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                driverAprStatusRepo.Edit(DriverAprStatus);
                driverAprStatusRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                driverAprStatusRepo.Delete(DriverAprStatus);
                bookingStatusRepo.Save();
                Id = 3;
            }
            else
            {
                driverAprStatusRepo.Add(DriverAprStatus);
                driverAprStatusRepo.Save();
                Id = DriverAprStatus.PKID;

            }


            return Json(Id, JsonRequestBehavior.AllowGet);
        }
        #endregion


        #region Drive CarApprove Status
        public ActionResult CarBookingStatus()
        {
            var CarBookingStatus = bookingStatusRepo.GetAll().ToList();
            ViewBag.CarBookingStatus = CarBookingStatus;
            var model = new M_Booking_Status();
            return View(model);
        }


        [HttpPost, ValidateInput(false)]
        // [ValidateAntiForgeryToken]
        public JsonResult CarBookingStatus(RLT_BOOKING_STATUS Obj1)
        {
            RLT_BOOKING_STATUS CarBookingStatus = new RLT_BOOKING_STATUS();
            CarBookingStatus.PKID = Obj1.PKID;
            CarBookingStatus.BOOKING_STATUS = Obj1.BOOKING_STATUS;

            int Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                CarBookingStatus.Is_Active = Obj1.Is_Active;
                bookingStatusRepo.Edit(CarBookingStatus);
                bookingStatusRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                bookingStatusRepo.Delete(CarBookingStatus);
                bookingStatusRepo.Save();
                Id = 3;
            }
            else
            {
                CarBookingStatus.Is_Active = true;
                bookingStatusRepo.Add(CarBookingStatus);
                bookingStatusRepo.Save();
                Id = 1;

            }
            return Json(Id, JsonRequestBehavior.AllowGet);
        }
        #endregion


        #region DocumentName
        public ActionResult DocumentName()
        {
            var vendorNameList = docNameRepo.GetAll().ToList();
            ViewBag.vendorNameList = vendorNameList;
            return View();
        }


        [HttpPost, ValidateInput(false)]

        // [ValidateAntiForgeryToken]
        public JsonResult DocumentName(M_Document_Name obj1)
        {
            RLT_DOCUMNETS_NAME obj = map.DocsName(obj1);

            long Id = 0;
            if (Request.Form["hdnOperation"].Equals("U"))
            {
                docNameRepo.Edit(obj);
                docNameRepo.Save();
                Id = 2;
            }
            else if (Request.Form["hdnOperation"].Equals("D"))
            {
                docNameRepo.Delete(obj);
                docNameRepo.Save();
                Id = 3;
            }
            else
            {
                obj.Is_Active = true;
                docNameRepo.Add(obj);
                docNameRepo.Save();
                Id = 1;

            }
            return Json(Id, JsonRequestBehavior.AllowGet);
        }
        #endregion

        #region TempBooking

        public ActionResult TempBookingResult()
        {
            var BookingRepo = tempBookingRepo.GetAll().ToList();
            ViewBag.BookingRepo = BookingRepo;

            return View();
        }
        #endregion

        [NonAction]
        public void LogOff()
        {
            FormsAuthentication.SignOut();

            RedirectToAction("Index", "Login", null);
        }
    }
    public class DashboardCountsDto
    {
        public int TotalVendors { get; set; }
        public int TotalCars { get; set; }
        public int TotalDrivers { get; set; }
        public int TotalBookings { get; set; }
    }

}