﻿@model DBLinker.Lib.Model.M_Booking

@{
    ViewBag.Title = "Booking Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Booking Detail for @Model.Booking_Id</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="../BookingManagement/BookingList">Booking List </a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>

                    <li class="active">View Booking</li>
                </ol>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-12">
                <div class="card-box">
                    <div class="card-head">
                        <header style="display: flex; justify-content: space-between; align-items: center;">
                            <h4 style="margin: 0;">Cab Details</h4>
                            @if (Model.IsAdminBooked == true)
                            {
                                <span style="background-color: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: normal;">
                                    <i class="fa fa-user-shield"></i> Admin Booked
                                </span>
                            }
                        </header>
                    </div>
                    <div class="card-body row">
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.City_From, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">City From</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.City_To, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">City To</label>
                            </div>
                        </div>

                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Trip_Type, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Trip Type</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Car_Category, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Car Category</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-dirty">
                                @Html.TextBoxFor(x => x.Distance, new { @class = "mdl-textfield__input", Style = "color:black;" })
                                <label class="mdl-textfield__label">Distance</label>
                            </div>
                        </div>

                    </div>

                    <div class="card-head">
                        <header>Payment Details</header>
                    </div>
                    <div class="card-body row">
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Mode_Of_Payment, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Mode Of Payment</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                <input type="text" class="mdl-textfield__input" value="@(Model.PaymentOption == 1 ? "Partial" : Model.PaymentOption == 2 ? "Full Online" : "Cash")" readonly />
                                <label class="mdl-textfield__label">Payment Type</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-dirty">
                                @Html.TextBoxFor(x => x.GST, new { @class = "mdl-textfield__input", Style = "color:black;" })
                                <label class="mdl-textfield__label">GST Amount</label>
                            </div>
                        </div>
                        @{
                            decimal totalFare = (Model.Basic_Fare ?? 0) + (Model.GST ?? 0);
                        }
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width is-upgraded is-dirty">
                                <input type="text" class="mdl-textfield__input" value="@totalFare.ToString("F2")" readonly style="color: black;" />
                                <label class="mdl-textfield__label">Total Fare</label>
                            </div>
                        </div>
                        @{
                            // Determine actual amount paid based on payment status and type
                            decimal amountPaid = 0;
                            decimal remainingAmount = 0;

                            bool isPaymentCompleted = Model.razorpay_status == "Paid" || Model.razorpay_status == "paid" ||
                                                      Model.razorpay_status == "Success" || Model.razorpay_status == "success" ||
                                                      Model.razorpay_status == "COMPLETED" || Model.razorpay_status == "completed";

                            // Check for partial payment using both PaymentOption and PaymentType fields
                            // PaymentOption: 1=PartialPay, 2=FullPay, 3=FullPayToDriver
                            bool isPartialPayment = (Model.PaymentOption == 1) ||
                                                   (Model.PaymentType == "PARTIAL") ||
                                                   (Model.PaymentType == "partial");

                            // Check for full online payment
                            bool isFullOnlinePayment = (Model.PaymentOption == 2) ||
                                                      (Model.PaymentType == "FULL") ||
                                                      (Model.PaymentType == "full");

                            // Debug information (can be removed in production)
                            string debugInfo = $"Status: '{Model.razorpay_status}', PaymentOption: {Model.PaymentOption}, PaymentType: '{Model.PaymentType}', PartialAmount: {Model.PartialPaymentAmount}, IsCompleted: {isPaymentCompleted}, IsPartial: {isPartialPayment}";

                            if (isPartialPayment) // Partial payment
                            {
                                if (isPaymentCompleted)
                                {
                                    amountPaid = Model.PartialPaymentAmount.GetValueOrDefault();
                                    remainingAmount = Model.RemainingAmountForDriver.GetValueOrDefault();
                                }
                                else
                                {
                                    amountPaid = 0; // No payment completed yet
                                    remainingAmount = Model.Fare.GetValueOrDefault(); // Full amount still pending
                                }
                            }
                            else if (isFullOnlinePayment) // Full online payment
                            {
                                if (isPaymentCompleted)
                                {
                                    amountPaid = Model.Fare.GetValueOrDefault();
                                    remainingAmount = 0;
                                }
                                else
                                {
                                    amountPaid = 0; // No payment completed yet
                                    remainingAmount = Model.Fare.GetValueOrDefault(); // Full amount still pending
                                }
                            }
                            else // Cash payment or other
                            {
                                amountPaid = 0; // Cash payments are not "paid" online
                                remainingAmount = Model.CashAmountToPayDriver.GetValueOrDefault();
                            }
                        }

                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                <input type="text" class="mdl-textfield__input" value="@amountPaid.ToString("F2")" readonly style="color: @(amountPaid > 0 ? "green" : "red");" />
                                <label class="mdl-textfield__label">Amount Paid Online</label>
                            </div>
                        </div>
                        <div class="col-lg-4 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                <input type="text" class="mdl-textfield__input" value="@remainingAmount.ToString("F2")" readonly style="color: @(remainingAmount > 0 ? "orange" : "green");" />
                                <label class="mdl-textfield__label">@(isPartialPayment ? "Remaining for Driver" : "Remaining Amount")</label>
                            </div>
                        </div>
                    </div>

                    @* Payment Details Section *@
                    @if (!string.IsNullOrEmpty(Model.payment_link))
                    {
                        <div class="card-body row">
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <input type="text" class="mdl-textfield__input" value="@Model.razorpay_status" readonly style="color: @(Model.razorpay_status == "Link Sent" ? "orange" : Model.razorpay_status == "Paid" ? "green" : "red");" />
                                    <label class="mdl-textfield__label">Payment Status</label>
                                </div>
                            </div>
                            <div class="col-lg-4 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    <input type="text" class="mdl-textfield__input" value="@Model.payment_link" readonly />
                                    <label class="mdl-textfield__label">Payment Link</label>
                                </div>
                            </div>
                            @if (!string.IsNullOrEmpty(Model.razorpay_order_id))
                            {
                                <div class="col-lg-4 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        <input type="text" class="mdl-textfield__input" value="@Model.razorpay_order_id" readonly />
                                        <label class="mdl-textfield__label">PhonePe Order Id</label>
                                    </div>
                                </div>
                            }
                            @if (Model.razorpay_status == "Link Sent" && !string.IsNullOrEmpty(Model.payment_link) && Model.payment_link.StartsWith("http"))
                            {
                                <div class="col-lg-12 p-t-20" style="text-align: center;">
                                    <a href="@Model.payment_link" target="_blank" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect btn-success">
                                        <i class="fa fa-external-link"></i> Open Payment Link
                                    </a>
                                    <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect btn-info" onclick="copyPaymentLink()">
                                        <i class="fa fa-copy"></i> Copy Link
                                    </button>
                                    <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect btn-warning" onclick="resendPaymentSMS()">
                                        <i class="fa fa-paper-plane"></i> Resend SMS
                                    </button>
                                </div>
                            }
                        </div>
                    }

                    <div class="card-head">
                        <header>Booking Details</header>
                    </div>
                    <div class="card-body row">


                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                @Html.TextBoxFor(x => x.PickUp_Date, "{0:dd/MM/yyyy}", new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">PickUp Date</label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.PickUp_Time, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">PickUp Time</label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.PickUp_Address, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">PickUp Address</label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.DropOff_Address, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">DropOff Address</label>
                            </div>
                        </div>

                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.completepickupaddress, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Complete Pickup Address</label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.completedropoffpaddress, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"> Complete DropOff Address</label>
                            </div>
                        </div>

                    </div>


                    <div class="card-head">
                        <header>Personal Details</header>
                    </div>
                    <div class="card-body row">


                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Name, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Name</label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Mail_Id, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Mail Id</label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Mobile_No1, new { @class = "mdl-textfield__input txtbx" })
                                <label class="mdl-textfield__label">Mobile No 1</label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Mobile_No2, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label">Mobile No 2</label>
                            </div>
                        </div>
                    </div>





                    @if (!string.IsNullOrEmpty(Model.Car_Number))
                    {
                        <div class="card-head">
                            <header>Assign Car</header>
                        </div>
                        <div class="card-body row">
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Vendor_Name, new { @class = "mdl-textfield__input" })
                                    <label class="mdl-textfield__label"><span>Vendor Name</span></label>
                                </div>
                            </div>

                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                    @Html.TextBoxFor(x => x.Car_Number, new { @class = "mdl-textfield__input" })
                                    <label class="mdl-textfield__label"><span>Car Number</span></label>
                                </div>
                            </div>
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @Html.TextBoxFor(x => x.Driver_Name, new { @class = "mdl-textfield__input" })
                                    <label class="mdl-textfield__label"><span>Driver Name</span></label>
                                </div>
                            </div>

                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                    @Html.TextBoxFor(x => x.Driver_Number, new { @class = "mdl-textfield__input" })
                                    <label class="mdl-textfield__label "><span>Driver Number</span></label>
                                </div>
                            </div>
                        </div>

                    }
                    <div class="card-head">
                        <header> Remark On Booking</header>
                    </div>

                    <div class="card-body row">
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.BookingEditRemark, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Booking Edit Remark</span></label>
                            </div>
                        </div>
                        <div class="col-lg-6 p-t-20">
                            <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                @Html.TextBoxFor(x => x.Booking_Remark, new { @class = "mdl-textfield__input" })
                                <label class="mdl-textfield__label"><span>Remark</span></label>
                            </div>
                        </div>
                    </div>




                </div>
            </div>
        </div>
    </div>
</div>
<script>
function copyPaymentLink() {
    var paymentLink = '@Model.payment_link';
    if (navigator.clipboard) {
        navigator.clipboard.writeText(paymentLink).then(function() {
            alert('Payment link copied to clipboard!');
        }, function(err) {
            console.error('Could not copy text: ', err);
            fallbackCopyTextToClipboard(paymentLink);
        });
    } else {
        fallbackCopyTextToClipboard(paymentLink);
    }
}

function fallbackCopyTextToClipboard(text) {
    var textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
        var successful = document.execCommand('copy');
        var msg = successful ? 'successful' : 'unsuccessful';
        alert('Payment link copied to clipboard!');
    } catch (err) {
        alert('Could not copy payment link');
    }
    document.body.removeChild(textArea);
}

function resendPaymentSMS() {
    if (confirm('Are you sure you want to resend the payment link SMS?')) {
        var bookingId = '@Model.Booking_Id';

        // Show loading state
        var button = event.target;
        var originalText = button.innerHTML;
        button.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Sending...';
        button.disabled = true;

        $.ajax({
            url: '@Url.Action("ResendPaymentLinkSMS", "BookingManagement")',
            type: 'POST',
            data: { bookingId: bookingId },
            success: function(response) {
                if (response.success) {
                    alert('Payment link SMS sent successfully!');
                } else {
                    alert('Error sending SMS: ' + (response.message || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                alert('Error sending SMS: ' + error);
            },
            complete: function() {
                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;
            }
        });
    }
}
</script>