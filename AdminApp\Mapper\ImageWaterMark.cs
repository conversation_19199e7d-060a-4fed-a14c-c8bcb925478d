﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;


namespace AdminApp.Mapper
{
    public class ImageWaterMark:Controller
    {

        public void AddImageWaterMark(String file, HttpPostedFileBase postedFile)
        {
            if (postedFile != null)
            {
                // Ensure directory exists
                string directory = Path.GetDirectoryName(file);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                string value = "CABYARRI.COM";
                using (Bitmap bitmap = new Bitmap(postedFile.InputStream, false))
                {
                    using (Graphics graphics = Graphics.FromImage(bitmap))
                    {
                        Brush brush = new SolidBrush(Color.Red);
                        Font font = new Font("Arial", 80, FontStyle.Italic, GraphicsUnit.Pixel);
                        SizeF textSize = new SizeF();
                        textSize = graphics.MeasureString(value, font);
                        Point position = new Point(bitmap.Width - ((int)textSize.Width + 10), bitmap.Height - ((int)textSize.Height + 10));
                        graphics.DrawString(value, font, brush, position);

                        Image newImage = (Image)bitmap;
                        newImage.Save(file);
                        graphics.Dispose();
                    }
                }
            }
        }
    }
}