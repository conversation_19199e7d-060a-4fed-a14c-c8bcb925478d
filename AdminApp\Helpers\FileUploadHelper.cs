using System;
using System.IO;
using System.Web;

namespace AdminApp.Helpers
{
    public static class FileUploadHelper
    {
        /// <summary>
        /// Ensures the directory exists before saving a file
        /// </summary>
        /// <param name="filePath">Full file path where the file will be saved</param>
        public static void EnsureDirectoryExists(string filePath)
        {
            try
            {
                string directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
            }
            catch (Exception ex)
            {
                // Log the exception if you have logging configured
                throw new InvalidOperationException($"Failed to create directory for file: {filePath}", ex);
            }
        }

        /// <summary>
        /// Safely saves a posted file to the specified path, ensuring directory exists
        /// </summary>
        /// <param name="file">The posted file</param>
        /// <param name="savePath">Full path where to save the file</param>
        public static void SaveFile(HttpPostedFileBase file, string savePath)
        {
            if (file == null || file.ContentLength == 0)
                return;

            EnsureDirectoryExists(savePath);
            file.SaveAs(savePath);
        }

        /// <summary>
        /// Gets the standard upload directories used by the application
        /// </summary>
        public static class UploadDirectories
        {
            public const string VendorPhoto = "~/Docs/VendorPhoto/";
            public const string CarOwnerPhoto = "~/Docs/CarOwnerPhoto/";
            public const string DriverPhoto = "~/Docs/DriverPhoto/";
            public const string CabYaariServices = "~/Docs/CabYaariServices/";
            public const string CarCategory = "~/Docs/CarCategory/";
            public const string DiscountCouponManager = "~/Docs/DiscountCouponManager/";
            public const string UserPhoto = "~/Docs/UserPhoto/";
            public const string CarDocs = "~/Docs/CarDocs/";
            public const string DriverDocs = "~/Docs/DriverDocs/";
            public const string VendorDocs = "~/Docs/VendorDocs/";
            public const string MostFavouriteCityImg = "~/Docs/MostFavouriteCityImg/";
        }
    }
}
