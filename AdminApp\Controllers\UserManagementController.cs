﻿using System;
using System.Collections.Generic;
using System.Data.Entity.SqlServer;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using AdminApp.Mapper;
using Common.Lib;
using DBLinker.Lib;
using DBLinker.Lib.Model;
using DBLinker.Lib.Repository;
using System.Drawing;
using System.Drawing.Imaging;
using System.Web.Query.Dynamic;
using System.Data.Entity;
using AdminApp.Filters;
using AdminApp.Services;

namespace AdminApp.Controllers
{

    public class UserManagementController : Controller
    {
        RLTDBContext entity;

        public UserManagementController()
        {
            entity = new DBLinker.Lib.RLTDBContext();
        }

        #region UserManager View/Add/Edit/Delete/Active deactive 

        [RoleBaseAuthentication("UserManager", RoleBaseAuthentication.ActionType.View)]
        public ActionResult UserManager()
        {
            var RoleList = entity.RLT_ADMIN_ROLE.ToList();
            ViewBag.RoleList = RoleList;

            var vendorList = entity.RLT_Vendor_Details.ToList();
            ViewBag.vendorList = vendorList;

            var EmployeeList = entity.RLT_Admin_User_Info.ToList();
            ViewBag.EmployeeList = EmployeeList;

            var DepartmentList = entity.RLT_Department_Name.ToList();
            ViewBag.DepartmentList = DepartmentList;


            var AdminUserList = (from a in entity.RLT_ADMIN_USER
                                 join b in entity.RLT_ADMIN_ROLE on a.RoleId equals b.RoleId
                                 select new M_Admin_User
                                 {
                                     PKID = a.PKID,
                                     RoleId = a.RoleId,
                                     RoleName = b.Role,
                                     UserName = a.UserName,
                                     UserPWD = a.UserPWD,
                                     UserPhoto = a.UserPhoto,
                                     UserFirstName = a.UserFirstName,
                                     UserMiddleName =a.UserMiddleName,
                                     UserLastName = a.UserLastName,
                                     UserEmailID = a.UserEmailID,
                                     UserMobileNo = a.UserMobileNo,
                                     AadharId = a.AadharId,
                                     Address = a.Address,
                                     Description = a.Description,
                                     VendorId = a.VendorId,
                                     CarOwnerId = a.CarOwnerId,
                                     EmployeeId = a.EmployeeId,
                                     DepartmentId = a.DepartmentId,
                                     IsActive = a.IsActive

                                 }).ToList();

            ViewBag.AdminUserList = AdminUserList;
            var model = new M_Admin_User();
            return View(model);
        }

        [HttpPost]

        [RoleBaseAuthentication("UserManager", RoleBaseAuthentication.ActionType.Add)]
        public JsonResult AddUserManager(M_Admin_User User)
        {
            string msg = ValidateUser(User, 0);
            if (msg.Length > 1) return Json(msg, JsonRequestBehavior.AllowGet);
            RLT_ADMIN_USER user = new RLT_ADMIN_USER();
            HttpPostedFileBase file = Request.Files[0];
            if (file != null && file.ContentLength > 0)
            {
                string path = Guid.NewGuid() + "_" + file.FileName;
                string Savepath = Server.MapPath("~/Docs/UserPhoto/") + path;

                // Ensure directory exists
                string directory = Path.GetDirectoryName(Savepath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                file.SaveAs(Savepath);
                user.UserPhoto = path;
            }
            user.RoleId = User.RoleId;
            user.UserName = User.UserName;
            user.UserPWD = QueryStringEncoding.EncryptString(User.UserPWD, "UserPass");
            user.UserFirstName = User.UserFirstName;
            user.UserMiddleName = User.UserMiddleName;
            user.UserLastName = User.UserLastName;
            user.UserEmailID = User.UserEmailID;
            user.UserMobileNo = User.UserMobileNo;
            user.IsActive = User.IsActive;
            user.Is2TfaAuthentication = false;
            user.CreatedBy = UserServices.getCurrentUserId();
            user.CreatedDate = DateTime.Now;
            entity.RLT_ADMIN_USER.Add(user);
            return entity.SaveChanges() > 0 ? Json(true, JsonRequestBehavior.AllowGet) : Json(false, JsonRequestBehavior.AllowGet);
        }

        [HttpGet]
        [RoleBaseAuthentication("UserManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult EditUserManager(int id = 0)
        {
            var data = entity.RLT_ADMIN_USER.Where(x => x.PKID == id).FirstOrDefault();
            if (data != null) data.UserPWD = QueryStringEncoding.DecryptString(data.UserPWD, "UserPass");
            return data != null ? Json(data, JsonRequestBehavior.AllowGet) : Json(false, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("UserManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult EditUserManager(M_Admin_User user)
        {
            string msg = ValidateUser(user, user.PKID);
            if (msg.Length > 1) return Json(msg, JsonRequestBehavior.AllowGet);
            HttpPostedFileBase file = Request.Files[0];
            var retrieveUser = entity.RLT_ADMIN_USER.Where(x => x.PKID == user.PKID).FirstOrDefault();
            if (retrieveUser != null)
            {
                if (file != null && file.ContentLength > 0)
                {
                    string path = Guid.NewGuid() + "_" + file.FileName;
                    string Savepath = Server.MapPath("~/Docs/UserPhoto/") + path;
                    file.SaveAs(Savepath);
                    retrieveUser.UserPhoto = path;
                }
                retrieveUser.RoleId = user.RoleId;
                retrieveUser.UserName = user.UserName;
                retrieveUser.UserPWD = QueryStringEncoding.EncryptString(user.UserPWD, "UserPass");
                retrieveUser.UserFirstName = user.UserFirstName;
                retrieveUser.UserMiddleName = user.UserMiddleName;
                retrieveUser.UserLastName = user.UserLastName;
                retrieveUser.UserEmailID = user.UserEmailID;
                retrieveUser.UserMobileNo = user.UserMobileNo;
                retrieveUser.IsActive = user.IsActive;
                retrieveUser.UpdatedBy = UserServices.getCurrentUserId();
                retrieveUser.UpdatedDate = DateTime.Now;
                int resulChange = entity.SaveChanges();
                return Json(resulChange, JsonRequestBehavior.AllowGet);
            }
            return Json(false, JsonRequestBehavior.AllowGet);
        }

        [NonAction]
        public string ValidateUser(M_Admin_User user, int id)
        {
            if (entity.RLT_ADMIN_USER.Any(x => x.UserName.ToLower() == user.UserName.ToLower() && x.PKID != id))
                return "UserName allready exist";

            else if (entity.RLT_ADMIN_USER.Any(x => x.UserMobileNo.ToLower() == user.UserMobileNo.ToLower() && x.PKID != id))
                return "Mobile Number allready exist";

            else if (entity.RLT_ADMIN_USER.Any(x => x.UserEmailID.ToLower() == user.UserEmailID.ToLower() && x.PKID != id))
                return "Email allready exist";
            else return "";
        }

        [HttpPost]
        public JsonResult DeleteUserManager(int id = 0)
        {
            int result = 0;
            var retrieveUser = entity.RLT_ADMIN_USER.Where(x => x.PKID == id).FirstOrDefault();
            if (retrieveUser != null)
            {
                retrieveUser.IsActive = false;
                retrieveUser.IsDeleted = true;
                retrieveUser.UpdatedDate = DateTime.Now;
                retrieveUser.UpdatedBy = UserServices.getCurrentUserId();
                result = entity.SaveChanges();
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("UserManager", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult ActiveDeactiveUserManager(int id = 0)
        {
            bool result = false;
            var retrieveUser = entity.RLT_ADMIN_USER.Where(x => x.PKID == id).FirstOrDefault();
            if (retrieveUser != null)
            {
                retrieveUser.IsActive = retrieveUser.IsActive == true ? false : true;
                retrieveUser.UpdatedDate = DateTime.Now;
                retrieveUser.UpdatedBy = UserServices.getCurrentUserId();
                result = entity.SaveChanges() > 0 ? true : false;
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        #endregion UserManager View/Add/Edit/Delete/Active deactive


        [RoleBaseAuthentication]
        public ActionResult RoleMenuMapping()
        {
            M_Role_Menu_Mapping Role_Menu_Mapping = new M_Role_Menu_Mapping();
            var RoleList = entity.RLT_ADMIN_ROLE.ToList();
            ViewBag.RoleList = RoleList;
            return View(Role_Menu_Mapping);
        }

        [RoleBaseAuthentication("RoleMenuMapping", RoleBaseAuthentication.ActionType.View)]
        public ActionResult _RoleMenuMapping(int RoleId = 0)
        {

            M_Role_Menu_Mapping Role_Menu_Mapping = new M_Role_Menu_Mapping();

            Role_Menu_Mapping.eRoleMenuMappingList = (from a in entity.RLT_MENU_MASTER
                                                      join b in entity.RLT_ROLE_MENU_MAPPING on
                                                       new
                                                       { Menu_Id = a.PKID, Role_IdNew = RoleId } equals
                                                     new { Menu_Id = b.Menu_Id, Role_IdNew = b.Role_Id }
                                                     into ps
                                                      from b in ps.DefaultIfEmpty()
                                                      orderby a.OrderNumber
                                                      select new M_Role_Menu_Mapping
                                                      {
                                                          Menu_Id = a.PKID,
                                                          MenuName = a.MenuName,
                                                          Is_View = b.Is_View == null ? false : b.Is_View,
                                                          Is_Add = b.Is_Add == null ? false : b.Is_Add,
                                                          Is_Edit = b.Is_Edit == null ? false : b.Is_Edit,
                                                          Is_Delete = b.Is_Delete == null ? false : b.Is_Delete,
                                                          MenuIcon = a.MenuIcon,
                                                          MenuURL = a.MenuURL,
                                                          CreatedDate = a.CreatedDate,
                                                          OrderNumber = a.OrderNumber,
                                                          ActionName = a.ActionName,
                                                          ControllerName = a.ControllerName
                                                      }).ToList();


            return PartialView(Role_Menu_Mapping);
        }

        [HttpPost]
        [RoleBaseAuthentication("RoleMenuMapping", RoleBaseAuthentication.ActionType.Add)]
        public ActionResult AddRoleMenuMapping(M_Role_Menu_Mapping obj)
        {


            foreach (var Items in obj.eRoleMenuMappingList)
            {
                var v = (from a in entity.RLT_ROLE_MENU_MAPPING where a.Role_Id == obj.Role_Id && a.Menu_Id == Items.Menu_Id select a);
                if (v.Count() > 0)
                {
                    foreach (RLT_ROLE_MENU_MAPPING vdtls in v)
                    {
                        vdtls.Is_View = Items.Is_View;
                        vdtls.Is_Add = Items.Is_Add;
                        vdtls.Is_Edit = Items.Is_Edit;
                        vdtls.Is_Delete = Items.Is_Delete;
                    }
                    entity.SaveChanges();
                }
                else
                {
                    RLT_ROLE_MENU_MAPPING vdtls = new RLT_ROLE_MENU_MAPPING();
                    vdtls.Role_Id = obj.Role_Id;
                    vdtls.Menu_Id = Items.Menu_Id;
                    vdtls.Is_View = Items.Is_View;
                    vdtls.Is_Add = Items.Is_Add;
                    vdtls.Is_Edit = Items.Is_Edit;
                    vdtls.Is_Delete = Items.Is_Delete;
                    entity.RLT_ROLE_MENU_MAPPING.Add(vdtls);
                    entity.SaveChanges();
                }
            }
            var currentUser = HttpContext.User as CustomPrincipal;
            var MenuList = (from a in entity.RLT_MENU_MASTER
                            join b in entity.RLT_ROLE_MENU_MAPPING on
                              a.PKID equals b.Menu_Id
                            where b.Role_Id == currentUser.roleId && b.Is_View == true
                            orderby a.OrderNumber
                            select new M_Role_Menu_Mapping
                            {
                                Menu_Id = a.PKID,
                                MenuName = a.MenuName,
                                MenuIcon = a.MenuIcon,
                                MenuURL = a.MenuURL,
                                PageId = a.PageId,
                                Is_View = b.Is_View,
                                Is_Add = b.Is_Add,
                                Is_Edit = b.Is_Edit,
                                Is_Delete = b.Is_Delete,
                                ParentMenuId = a.ParentMenuId
                            }).ToList();

            Session["MenuList"] = MenuList;
            return RedirectToAction("RoleMenuMapping");
        }

        public ActionResult _MenuPartial()
        {
            int role_Id = Convert.ToInt32(((AdminApp.Filters.CustomPrincipal)(User)).roleId);
            var MenuList = (from a in entity.RLT_MENU_MASTER
                            join b in entity.RLT_ROLE_MENU_MAPPING on
                              a.PKID equals b.Menu_Id
                            where b.Role_Id == role_Id
  && b.Is_View == true
                            orderby a.OrderNumber
                            select new M_Role_Menu_Mapping
                            {
                                Menu_Id = a.PKID,
                                MenuName = a.MenuName,
                                MenuIcon = a.MenuIcon,
                                MenuURL = a.MenuURL,
                                PageId = a.PageId,
                                Is_View = b.Is_View,
                                ParentMenuId = a.ParentMenuId
                            }).ToList();

            ViewBag.MenuList = MenuList;

            return PartialView("_MenuPartial");
        }


        [RoleBaseAuthentication("EmployeeList", RoleBaseAuthentication.ActionType.Edit)]
        public ActionResult Employee() // Add/Edit Employee
        {
            var CityList = entity.RLT_CITY.ToList().OrderBy(x => x.City_Name);
            ViewBag.CityList = CityList;

            var StateList = entity.RLT_STATE.ToList().OrderBy(x => x.State_Name);
            ViewBag.StateList = StateList;

            M_Employee Employee_Details = new M_Employee();
            if (Request.QueryString["id"] != null)
            {
                string DecData = QueryStringEncoding.DecryptString(Convert.ToString(Request.QueryString["id"]), "Employee");
                int PKID = Convert.ToInt16(DecData);

                Employee_Details = entity.RLT_ADMIN_USER.Join(entity.RLT_Admin_User_Info, u => u.PKID, ui => ui.UserId, (u, ui) => new { u, ui })
                   .Where(x => x.u.PKID == PKID).Select(s => new M_Employee
                   {
                       PKID = s.u.PKID,
                       Employee_Name = s.u.UserFirstName + " " + s.u.UserMiddleName + " " + s.u.UserLastName,
                       Photo = s.u.UserPhoto,
                       DOB = s.ui.DOB,
                       Phone_1 = s.u.UserMobileNo,
                       Phone_1_IsWhatsup = s.ui.Phone_1_IsWhatsup,
                       Phone_2 = s.ui.Phone_2,
                       Email = s.u.UserEmailID,
                       Gender = s.ui.Gender,
                       Marrital_Status = s.ui.Marrital_Status,
                       Spouses_Name = s.ui.Spouses_Name,
                       Father_Name = s.ui.Father_Name,
                       Mother_Name = s.ui.Mother_Name,
                       Employee_ID = s.ui.Employee_ID,
                       Designation = s.ui.Designation,
                       Department = s.ui.Department,
                       Supervisor = s.ui.Supervisor,
                       Work_Phone = s.ui.Work_Phone,
                       Work_Email = s.ui.Work_Email,
                       Work_StartDate = s.ui.Work_StartDate,
                       Work_City = s.ui.Work_City,
                       C10th_School = s.ui.C10th_School,
                       C10th_PassingYear = s.ui.C10th_PassingYear,
                       C10th_Percentage = s.ui.C10th_Percentage,
                       C12th_School = s.ui.C12th_School,
                       C12th_PassingYear = s.ui.C12th_PassingYear,
                       C12th_Percentage = s.ui.C12th_Percentage,
                       Degree_Name = s.ui.Degree_Name,
                       Degree_College = s.ui.Degree_College,
                       Degree_PassingYear = s.ui.Degree_PassingYear,
                       Degree_Percentage = s.ui.Degree_Percentage,
                       Master_Degree_Name = s.ui.Master_Degree_Name,
                       Master_Degree_College = s.ui.Master_Degree_College,
                       Master_Degree_PassingYear = s.ui.Master_Degree_PassingYear,
                       Master_Degree_Percentage = s.ui.Master_Degree_Percentage,

                       Current_Permanent_Same = s.ui.Current_Permanent_Same,
                       Current_Address1 = s.ui.Current_Address1,
                       Current_Address2 = s.ui.Current_Address2,
                       Permanent_Address1 = s.ui.Permanent_Address1,
                       Permanent_Address2 = s.ui.Permanent_Address2,
                       Current_Address_State = s.ui.Current_Address_State,
                       Current_Address_City = s.ui.Current_Address_City,
                       Permanent_Address_State = s.ui.Permanent_Address_State,
                       Permanent_Address_City = s.ui.Permanent_Address_City
                   }).FirstOrDefault();
                if (Employee_Details == null)
                {
                    var employeeName = entity.RLT_ADMIN_USER.Where(x => x.PKID == PKID).Select(x => new { name = x.UserFirstName + " " + x.UserMiddleName + " " + x.UserLastName, pkid = x.PKID }).FirstOrDefault();
                    M_Employee employee = new M_Employee();
                    employee.Employee_Name = employeeName.name;
                    employee.PKID = employeeName.pkid;
                    return View(employee);
                }
            }

            return View(Employee_Details);
        }

        [HttpPost]
        [RoleBaseAuthentication("EmployeeList", RoleBaseAuthentication.ActionType.Edit)]
        public ActionResult Employee(M_Employee User, string id) //Add/Edit Employee
        {
            int changes = 0, Pkid = Request.QueryString["id"] != null ? Convert.ToInt32(QueryStringEncoding.DecryptString(Convert.ToString(Request.QueryString["id"]), "Employee")) : Convert.ToInt32(User.PKID);
            if (Pkid > 0 )
            {
                var userInfo = entity.RLT_Admin_User_Info.Where(x => x.UserId == Pkid).FirstOrDefault();
                if (userInfo != null)
                {
                    RLT_Admin_User_Info updateUserInfo = new RLT_Admin_User_Info();
                    updateUserInfo.Employee_Name = User.Employee_Name;
                    updateUserInfo.DOB = User.DOB;
                    updateUserInfo.Phone_1 = User.Phone_1;
                    updateUserInfo.Phone_2 = User.Phone_2;
                    updateUserInfo.Email = User.Email;
                    updateUserInfo.Gender = User.Gender;
                    updateUserInfo.Marrital_Status = User.Marrital_Status;
                    updateUserInfo.Spouses_Name = User.Spouses_Name;
                    updateUserInfo.Father_Name = User.Father_Name;
                    updateUserInfo.Mother_Name = User.Mother_Name;

                    updateUserInfo.Employee_ID = User.Employee_ID;
                    updateUserInfo.Designation = User.Designation;
                    updateUserInfo.Department = User.Department;
                    updateUserInfo.Supervisor = User.Supervisor;
                    updateUserInfo.Work_Phone = User.Work_Phone;
                    updateUserInfo.Work_Email = User.Work_Email;
                    updateUserInfo.Work_StartDate = User.Work_StartDate;
                    updateUserInfo.Work_City = User.Work_City;

                    updateUserInfo.C10th_School = User.C10th_School;
                    updateUserInfo.C10th_PassingYear = User.C10th_PassingYear;
                    updateUserInfo.C10th_Percentage = User.C10th_Percentage;
                    updateUserInfo.C12th_School = User.C12th_School;
                    updateUserInfo.C12th_PassingYear = User.C12th_PassingYear;
                    updateUserInfo.C12th_Percentage = User.C12th_Percentage;
                    updateUserInfo.Degree_Name = User.Degree_Name;
                    updateUserInfo.Degree_College = User.Degree_College;
                    updateUserInfo.Degree_PassingYear = User.Degree_PassingYear;
                    updateUserInfo.Degree_Percentage = User.Degree_Percentage;

                    updateUserInfo.Master_Degree_Name = User.Master_Degree_Name;
                    updateUserInfo.Master_Degree_College = User.Master_Degree_College;
                    updateUserInfo.Master_Degree_PassingYear = User.Master_Degree_PassingYear;
                    updateUserInfo.Master_Degree_Percentage = User.Master_Degree_Percentage;

                    updateUserInfo.Current_Address1 = User.Current_Address1;
                    updateUserInfo.Current_Address2 = User.Current_Address2;
                    updateUserInfo.Current_Address_State = User.Current_Address_State;
                    updateUserInfo.Current_Address_City = User.Current_Address_City;
                    updateUserInfo.Permanent_Address1 = User.Permanent_Address1;
                    updateUserInfo.Permanent_Address2 = User.Permanent_Address2;
                    updateUserInfo.Permanent_Address_State = User.Permanent_Address_State;
                    updateUserInfo.Permanent_Address_City = User.Permanent_Address_City;
                    updateUserInfo.Current_Permanent_Same = User.Current_Permanent_Same;
                    
                    changes = entity.SaveChanges();
                }
                else
                {
                    RLT_Admin_User_Info addUserInfo = new RLT_Admin_User_Info();
                    addUserInfo.Employee_Name = User.Employee_Name;
                    addUserInfo.DOB = User.DOB;
                    addUserInfo.Phone_1 = User.Phone_1;
                    addUserInfo.Phone_2 = User.Phone_2;
                    addUserInfo.Email = User.Email;
                    addUserInfo.Gender = User.Gender;
                    addUserInfo.Marrital_Status = User.Marrital_Status;
                    addUserInfo.Spouses_Name = User.Spouses_Name;
                    addUserInfo.Father_Name = User.Father_Name;
                    addUserInfo.Mother_Name = User.Mother_Name;

                    addUserInfo.Employee_ID = User.Employee_ID;
                    addUserInfo.Designation = User.Designation;
                    addUserInfo.Department = User.Department;
                    addUserInfo.Supervisor = User.Supervisor;
                    addUserInfo.Work_Phone = User.Work_Phone;
                    addUserInfo.Work_Email = User.Work_Email;
                    addUserInfo.Work_StartDate = User.Work_StartDate;
                    addUserInfo.Work_City = User.Work_City;

                    addUserInfo.C10th_School = User.C10th_School;
                    addUserInfo.C10th_PassingYear = User.C10th_PassingYear;
                    addUserInfo.C10th_Percentage = User.C10th_Percentage;
                    addUserInfo.C12th_School = User.C12th_School;
                    addUserInfo.C12th_PassingYear = User.C12th_PassingYear;
                    addUserInfo.C12th_Percentage = User.C12th_Percentage;
                    addUserInfo.Degree_Name = User.Degree_Name;
                    addUserInfo.Degree_College = User.Degree_College;
                    addUserInfo.Degree_PassingYear = User.Degree_PassingYear;
                    addUserInfo.Degree_Percentage = User.Degree_Percentage;

                    addUserInfo.Master_Degree_Name = User.Master_Degree_Name;
                    addUserInfo.Master_Degree_College = User.Master_Degree_College;
                    addUserInfo.Master_Degree_PassingYear = User.Master_Degree_PassingYear;
                    addUserInfo.Master_Degree_Percentage = User.Master_Degree_Percentage;

                    addUserInfo.Current_Address1 = User.Current_Address1;
                    addUserInfo.Current_Address2 = User.Current_Address2;
                    addUserInfo.Current_Address_State = User.Current_Address_State;
                    addUserInfo.Current_Address_City = User.Current_Address_City;
                    addUserInfo.Permanent_Address1 = User.Permanent_Address1;
                    addUserInfo.Permanent_Address2 = User.Permanent_Address2;
                    addUserInfo.Permanent_Address_State = User.Permanent_Address_State;
                    addUserInfo.Permanent_Address_City = User.Permanent_Address_City;
                    addUserInfo.Current_Permanent_Same = User.Current_Permanent_Same;

                    addUserInfo.UserId = Convert.ToInt32(User.PKID);
                    entity.RLT_Admin_User_Info.Add(addUserInfo);
                    changes = entity.SaveChanges();
                }
            }
            return Json(changes, JsonRequestBehavior.AllowGet);
        }

        [RoleBaseAuthentication("EmployeeList", RoleBaseAuthentication.ActionType.View)]
        public ActionResult EmployeeView()
        {
            M_Employee Employee_Details = new M_Employee();
            if (Request.QueryString["id"] != null)
            {
                string DecData = QueryStringEncoding.DecryptString(Convert.ToString(Request.QueryString["id"]), "Employee");
                int PKID = Convert.ToInt16(DecData);

                var employee_Details = (
                     from user in entity.RLT_ADMIN_USER
                     join userInfo in entity.RLT_Admin_User_Info on user.PKID equals userInfo.UserId
                     join b in entity.RLT_CITY on userInfo.Work_City equals b.PKID into b1
                     from b in b1.DefaultIfEmpty()

                     join e in entity.RLT_STATE on userInfo.Current_Address_State equals e.PKID into e1
                     from e in e1.DefaultIfEmpty()
                     join f in entity.RLT_CITY on userInfo.Current_Address_City equals f.PKID into f1
                     from f in f1.DefaultIfEmpty()

                     join g in entity.RLT_STATE on userInfo.Permanent_Address_State equals g.PKID into g1
                     from g in g1.DefaultIfEmpty()
                     join h in entity.RLT_CITY on userInfo.Permanent_Address_City equals h.PKID into h1
                     from h in h1.DefaultIfEmpty()

                     where user.PKID == PKID
                     select new M_Employee
                     {
                         Employee_Name = user.UserFirstName + " " + user.UserMiddleName + " " + user.UserLastName,
                         Photo = user.UserPhoto,
                         DOB = userInfo.DOB,
                         Phone_1 = user.UserMobileNo,
                         Phone_1_IsWhatsup = userInfo.Phone_1_IsWhatsup,
                         Phone_2 = userInfo.Phone_2,
                         Email = user.UserEmailID,
                         Gender = userInfo.Gender,
                         Marrital_Status = userInfo.Marrital_Status,
                         Spouses_Name = userInfo.Spouses_Name,
                         Father_Name = userInfo.Father_Name,
                         Mother_Name = userInfo.Mother_Name,
                         Employee_ID = userInfo.Employee_ID,
                         Designation = userInfo.Designation,
                         Department = userInfo.Department,
                         Supervisor = userInfo.Supervisor,
                         Work_Phone = userInfo.Work_Phone,
                         Work_Email = userInfo.Work_Email,
                         Work_StartDate = userInfo.Work_StartDate,
                         Work_City_Name = b.City_Name,
                         C10th_School = userInfo.C10th_School,
                         C10th_PassingYear = userInfo.C10th_PassingYear,
                         C10th_Percentage = userInfo.C10th_Percentage,
                         C12th_School = userInfo.C12th_School,
                         C12th_PassingYear = userInfo.C12th_PassingYear,
                         C12th_Percentage = userInfo.C12th_Percentage,
                         Degree_Name = userInfo.Degree_Name,
                         Degree_College = userInfo.Degree_College,
                         Degree_PassingYear = userInfo.Degree_PassingYear,
                         Degree_Percentage = userInfo.Degree_Percentage,
                         Master_Degree_Name = userInfo.Master_Degree_Name,
                         Master_Degree_College = userInfo.Master_Degree_College,
                         Master_Degree_PassingYear = userInfo.Master_Degree_PassingYear,
                         Master_Degree_Percentage = userInfo.Master_Degree_Percentage,
                         Current_Address1 = userInfo.Current_Address1,
                         Current_Address2 = userInfo.Current_Address2,
                         Permanent_Address1 = userInfo.Permanent_Address1,
                         Permanent_Address2 = userInfo.Permanent_Address2,
                         Current_Address_State_Name = e.State_Name,
                         Current_Address_City_Name = f.City_Name,
                         Permanent_Address_State_Name = g.State_Name,
                         Permanent_Address_City_Name = h.City_Name

                     }).FirstOrDefault();
                if (employee_Details != null) return View(employee_Details);
            }

            return View(Employee_Details);

        }

        public ActionResult EmployeeList()
        {

            var EmployeeList = (from a in entity.RLT_Admin_User_Info
                                select new M_Employee
                                {
                                    PKID = a.PKID,
                                    Employee_Name = a.Employee_Name,
                                    Photo = a.Photo,
                                    Phone_1 = a.Phone_1,
                                    DOB = a.DOB,
                                    Employee_ID = a.Employee_ID,
                                    Designation = a.Designation,
                                    Is_Active = a.Is_Active,

                                }).ToList();

            ViewBag.EmployeeList = EmployeeList;
            return View();
        }

        public ActionResult EmployeeEnableDisable(int PKID, bool status) //Enable/Disable Employee
        {
            int Id = 0;
            if (PKID > 0)
            {
                var vdtlsList = (from a in entity.RLT_Admin_User_Info where a.PKID == PKID select a);
                foreach (RLT_Admin_User_Info v in vdtlsList)
                {

                    v.Is_Active = status;
                }
                entity.SaveChanges();
                Id = 2;
            }

            return Json(Id, JsonRequestBehavior.AllowGet);
        }

        #region Menu View/Add/Edit/Delete 
        [HttpGet]
        [RoleBaseAuthentication("MenuList", RoleBaseAuthentication.ActionType.View)]
        public ActionResult MenuList()
        {
            return View(entity.RLT_MENU_MASTER.ToList().OrderBy(x => x.OrderNumber));
        }

        [HttpPost]
        [RoleBaseAuthentication("MenuList", RoleBaseAuthentication.ActionType.Add)]
        public ActionResult MenuList(RLT_MENU_MASTER data)
        {
            data.CreatedDate = DateTime.Now;
            data.CreatedBy = UserServices.getCurrentUserId().ToString();
            entity.RLT_MENU_MASTER.Add(data);
            bool result = entity.SaveChanges() > 0 ? true : false;
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        [HttpGet]
        [RoleBaseAuthentication("MenuList", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult EditMenu(int id = 0)
        {
            var data = entity.RLT_MENU_MASTER.Where(x => x.PKID == id).FirstOrDefault();
            return data != null ? Json(data, JsonRequestBehavior.AllowGet) : Json(false, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("MenuList", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult EditMenu(RLT_MENU_MASTER data)
        {
            bool result = false;
            if (data.PKID > 0)
            {
                var getRecord = entity.RLT_MENU_MASTER.Where(x => x.PKID == data.PKID).FirstOrDefault();
                if (getRecord != null)
                {
                    getRecord.MenuName = data.MenuName;
                    getRecord.MenuIcon = data.MenuIcon;
                    getRecord.MenuURL = data.MenuURL;
                    getRecord.PageId = data.PageId;
                    getRecord.ParentMenuId = data.ParentMenuId;
                    getRecord.ControllerName = data.ControllerName;
                    getRecord.ActionName = data.ActionName;
                    getRecord.IsActive = data.IsActive;
                    getRecord.OrderNumber = data.OrderNumber;
                    getRecord.ActiveMenuClass = data.ActiveMenuClass;
                    getRecord.UpdatedDate = DateTime.Now;
                    getRecord.UpdatedBy = UserServices.getCurrentUserId();
                    result = entity.SaveChanges() > 0 ? true : false;
                }
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication(RoleBaseAuthentication.Mode.Ignore)]
        public JsonResult CheckDuplicateMenuRecord(RLT_MENU_MASTER data)
        {
            //if data is comming with pkid that mean this record already in our record so it will be here for updation checking...
            bool IsEdit = data.PKID == 0 ? false : true, IsDublicate = false, PageId = false, MenuURL = false, OrderNumber = false;
            if (data.MenuURL != null && data.PageId != null)
            {
                var getRecord = entity.RLT_MENU_MASTER.Where(x => x.MenuURL.ToLower() == data.MenuURL.ToLower() || x.PageId.ToLower() == data.PageId.ToLower() || data.OrderNumber == x.OrderNumber).ToList();
                if (getRecord != null)
                {
                    foreach (var item in getRecord)
                    {
                        if (item.PageId?.ToLower() == data.PageId?.ToLower())
                            PageId = (IsEdit && item.PKID == data.PKID) ? false : true;
                        if (item.MenuURL?.ToLower() == data.MenuURL?.ToLower())
                            MenuURL = (IsEdit && item.PKID == data.PKID) ? false : true;
                        if (item.OrderNumber == data.OrderNumber)
                            OrderNumber = (IsEdit && item.PKID == data.PKID) ? false : true;
                    }
                    IsDublicate = PageId || MenuURL || OrderNumber ? true : false;
                }
            }
            return Json(new { IsDublicate, PageId, MenuURL, OrderNumber }, JsonRequestBehavior.AllowGet);
        }

        [HttpPost]
        [RoleBaseAuthentication("MenuList", RoleBaseAuthentication.ActionType.Edit)]
        public JsonResult ActiveDeactiveMenuList(int id = 0)
        {
            bool result = false;
            if (id > 0)
            {
                var data = entity.RLT_MENU_MASTER.Where(x => x.PKID == id).FirstOrDefault();
                if (data != null)
                {
                    data.IsActive = data.IsActive != null ? data.IsActive == true ? false : true : false;
                    data.UpdatedBy = UserServices.getCurrentUserId();
                    data.UpdatedDate = DateTime.Now;
                    result = entity.SaveChanges() > 0 ? true : false;
                }
            }
            return Json(result, JsonRequestBehavior.AllowGet);
        }

        #endregion Menu View/Add/Edit/Delete
    }
}