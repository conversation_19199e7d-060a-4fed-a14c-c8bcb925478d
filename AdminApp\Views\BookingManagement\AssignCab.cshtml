﻿@model DBLinker.Lib.Model.M_Booking

@{
    ViewBag.Title = "Booking Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<div class="page-content-wrapper">
    <div class="page-content">
        <div class="page-bar">
            <div class="page-title-breadcrumb">
                <div class=" pull-left">
                    <div class="page-title">Assign Car for @Model.Booking_Id</div>
                </div>
                <ol class="breadcrumb page-breadcrumb pull-right">
                    <li>
                        <i class="fa fa-home"></i>&nbsp;<a class="parent-item" href="~/Admin/Dashboard">Home</a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li>
                        <a class="parent-item" href="../BookingManagement/BookingList">Booking List </a>&nbsp;<i class="fa fa-angle-right"></i>
                    </li>
                    <li class="active">Assign Car</li>
                </ol>
            </div>
        </div>

        @using (Html.BeginForm())
        {

            @Html.AntiForgeryToken()
            @Html.HiddenFor(x => x.PKID)
            <div class="row">
                <div class="col-sm-12">
                    <div class="card-box">

                        <div class="card-head">
                            <header>Action Against Booking</header>
                        </div>
                        <div class="card-body row">
                            <div class="col-lg-6 p-t-20">
                                <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                    @if (ViewBag.BookingStatusList != null)
                                    {

                                        @Html.DropDownListFor(x => x.Booking_Status_Id, new SelectList(ViewBag.BookingStatusList, "PKID", "BOOKING_STATUS"), "-- Select Booking Status --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                    }
                                    <label class="mdl-textfield__label required"><span>Booking Status</span></label>
                                </div>
                            </div>

                        </div>

                        <div id="rowCarAssign" style="display:none;">
                            <div class="card-head">
                                <header>Assign Car</header>
                            </div>
                            <div class="card-body row">
                                <div class="col-lg-6 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        @if (ViewBag.vendorList != null)
                                        {

                                            @Html.DropDownListFor(x => x.Vendor_PKID, new SelectList(ViewBag.vendorList, "PKID", "Vendor_Company_Name"), "-- Select Vendor --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })
                                        }
                                        <label class="mdl-textfield__label required"><span>Vendor Name</span></label>
                                    </div>
                                </div>

                                <div class="col-lg-6 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">

                                        @Html.DropDownListFor(x => x.Car_PKID, new SelectList(string.Empty, "Value", "Text"), "-- Select Car --", new { @class = "mdl-textfield__input", @style = "padding-top: 25px;" })

                                        <label class="mdl-textfield__label required"><span>Car</span></label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="rowRemark" style="display:none;">
                            <div class="card-head">
                                <header>Remark</header>
                            </div>
                            <div class="card-body row">
                                <div class="col-lg-12 p-t-20">
                                    <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label txt-full-width">
                                        @Html.TextBoxFor(x => x.Booking_Remark, new { @class = "mdl-textfield__input", @maxlength = "500" })
                                        <label class="mdl-textfield__label required"><span>Booking Remark</span> </label>
                                    </div>
                                </div>
                            </div>

                        </div>


                        <div class="col-lg-12 p-t-20 text-center">
                            <button type="button" class="mdl-button mdl-js-button mdl-button--raised mdl-js-ripple-effect m-b-10 m-r-20 btn-pink" id="btnSave">Save</button>
                        </div>

                    </div>
                </div>
            </div>


        }

    </div>
</div>

<script src="~/Scripts/CrudFile/BookingAssignCab.js?v=1"></script>

